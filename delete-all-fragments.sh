#!/bin/bash

# <PERSON>ript to delete all fragment files and layouts in favor of Compose screens

echo "Starting migration to pure Compose..."
echo "This script will delete ALL Fragment files and layouts"

PROJECT_ROOT="/Users/<USER>/Workspace/ThyView/android"
BACKUP_DIR="$PROJECT_ROOT/fragment_backup_$(date +%Y%m%d_%H%M%S)"

# Create backup directory
mkdir -p "$BACKUP_DIR"
echo "Created backup directory at $BACKUP_DIR"

# Function to backup and delete a file
backup_and_delete() {
    if [ -f "$1" ]; then
        # Create directory structure in backup
        backup_path="$BACKUP_DIR/$(dirname "${1#$PROJECT_ROOT/}")"
        mkdir -p "$backup_path"
        
        # Copy file to backup
        cp "$1" "$backup_path/"
        echo "Backed up: $1"
        
        # Delete original
        rm "$1"
        echo "Deleted: $1"
    else
        echo "Warning: File not found - $1"
    fi
}

echo "Backing up and deleting Fragment files..."

# Fragment Kotlin files
backup_and_delete "$PROJECT_ROOT/app/src/main/java/com/thyview/ui/home/<USER>"
backup_and_delete "$PROJECT_ROOT/app/src/main/java/com/thyview/ui/search/SearchFragment.kt"
backup_and_delete "$PROJECT_ROOT/app/src/main/java/com/thyview/ui/profile/ProfileFragment.kt"
backup_and_delete "$PROJECT_ROOT/app/src/main/java/com/thyview/ui/reviews/ReviewsFragment.kt"
backup_and_delete "$PROJECT_ROOT/app/src/main/java/com/thyview/ui/reviews/PostDetailFragment.kt"
backup_and_delete "$PROJECT_ROOT/app/src/main/java/com/thyview/ui/Articles/ArticlesFragment.kt"
backup_and_delete "$PROJECT_ROOT/app/src/main/java/com/thyview/ui/actor/ActorFragment.kt"
backup_and_delete "$PROJECT_ROOT/app/src/main/java/com/thyview/ui/movie/MovieFragment.kt"

# Fragment layout files
backup_and_delete "$PROJECT_ROOT/app/src/main/res/layout/fragment_home.xml"
backup_and_delete "$PROJECT_ROOT/app/src/main/res/layout/fragment_search.xml"
backup_and_delete "$PROJECT_ROOT/app/src/main/res/layout/fragment_profile.xml"
backup_and_delete "$PROJECT_ROOT/app/src/main/res/layout/fragment_dashboard.xml"
backup_and_delete "$PROJECT_ROOT/app/src/main/res/layout/fragment_actor.xml"
backup_and_delete "$PROJECT_ROOT/app/src/main/res/layout/fragment_movie.xml"

echo "Migration complete!"
echo "Files have been backed up to $BACKUP_DIR"
echo "Please verify that all Compose screens are working correctly."