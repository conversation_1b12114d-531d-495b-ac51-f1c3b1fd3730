kotlin version: 2.0.0
error message: Incremental compilation failed: java.lang.IndexOutOfBoundsException
java.lang.RuntimeException: java.lang.IndexOutOfBoundsException
	at org.jetbrains.kotlin.com.intellij.util.io.PersistentEnumeratorBase.catchCorruption(PersistentEnumeratorBase.java:628)
	at org.jetbrains.kotlin.com.intellij.util.io.PersistentEnumeratorBase.doEnumerate(PersistentEnumeratorBase.java:266)
	at org.jetbrains.kotlin.com.intellij.util.io.PersistentEnumeratorBase.tryEnumerate(PersistentEnumeratorBase.java:257)
	at org.jetbrains.kotlin.com.intellij.util.io.PersistentMapImpl.doGet(PersistentMapImpl.java:583)
	at org.jetbrains.kotlin.com.intellij.util.io.PersistentMapImpl.get(PersistentMapImpl.java:545)
	at org.jetbrains.kotlin.com.intellij.util.io.PersistentHashMap.get(PersistentHashMap.java:178)
	at org.jetbrains.kotlin.incremental.storage.LazyStorage.get(LazyStorage.kt:74)
	at org.jetbrains.kotlin.incremental.storage.InMemoryStorage.get(InMemoryStorage.kt:68)
	at org.jetbrains.kotlin.incremental.storage.PersistentStorageWrapper.get(PersistentStorage.kt:90)
	at org.jetbrains.kotlin.incremental.AbstractIncrementalCache.getSourceFileIfClass(AbstractIncrementalCache.kt:109)
	at org.jetbrains.kotlin.incremental.IncrementalJvmCompilerRunner.additionalDirtyFiles(IncrementalJvmCompilerRunner.kt:391)
	at org.jetbrains.kotlin.incremental.IncrementalJvmCompilerRunner.additionalDirtyFiles(IncrementalJvmCompilerRunner.kt:62)
	at org.jetbrains.kotlin.incremental.IncrementalCompilerRunner.doCompile(IncrementalCompilerRunner.kt:526)
	at org.jetbrains.kotlin.incremental.IncrementalCompilerRunner.compileImpl(IncrementalCompilerRunner.kt:423)
	at org.jetbrains.kotlin.incremental.IncrementalCompilerRunner.tryCompileIncrementally$lambda$9$compile(IncrementalCompilerRunner.kt:249)
	at org.jetbrains.kotlin.incremental.IncrementalCompilerRunner.tryCompileIncrementally(IncrementalCompilerRunner.kt:267)
	at org.jetbrains.kotlin.incremental.IncrementalCompilerRunner.compile(IncrementalCompilerRunner.kt:120)
	at org.jetbrains.kotlin.daemon.CompileServiceImplBase.execIncrementalCompiler(CompileServiceImpl.kt:676)
	at org.jetbrains.kotlin.daemon.CompileServiceImplBase.access$execIncrementalCompiler(CompileServiceImpl.kt:92)
	at org.jetbrains.kotlin.daemon.CompileServiceImpl.compile(CompileServiceImpl.kt:1661)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at java.rmi/sun.rmi.server.UnicastServerRef.dispatch(UnicastServerRef.java:360)
	at java.rmi/sun.rmi.transport.Transport$1.run(Transport.java:200)
	at java.rmi/sun.rmi.transport.Transport$1.run(Transport.java:197)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:712)
	at java.rmi/sun.rmi.transport.Transport.serviceCall(Transport.java:196)
	at java.rmi/sun.rmi.transport.tcp.TCPTransport.handleMessages(TCPTransport.java:587)
	at java.rmi/sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run0(TCPTransport.java:828)
	at java.rmi/sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.lambda$run$0(TCPTransport.java:705)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:399)
	at java.rmi/sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run(TCPTransport.java:704)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: java.lang.IndexOutOfBoundsException
	at java.base/java.nio.Buffer.checkIndex(Buffer.java:743)
	at java.base/java.nio.DirectByteBuffer.get(DirectByteBuffer.java:339)
	at org.jetbrains.kotlin.com.intellij.util.io.DirectBufferWrapper.get(DirectBufferWrapper.java:62)
	at org.jetbrains.kotlin.com.intellij.util.io.keyStorage.AppendableStorageBackedByResizableMappedFile$3.write(AppendableStorageBackedByResizableMappedFile.java:207)
	at java.base/java.io.OutputStream.write(OutputStream.java:162)
	at java.base/java.io.DataOutputStream.writeInt(DataOutputStream.java:208)
	at org.jetbrains.kotlin.com.intellij.util.io.IOUtil.writeString(IOUtil.java:58)
	at org.jetbrains.kotlin.incremental.storage.StringExternalizer.save(externalizers.kt:224)
	at org.jetbrains.kotlin.incremental.storage.ExternalizersKt.writeString(externalizers.kt:254)
	at org.jetbrains.kotlin.incremental.storage.FqNameExternalizer.save(externalizers.kt:84)
	at org.jetbrains.kotlin.incremental.storage.FqNameExternalizer.save(externalizers.kt:81)
	at org.jetbrains.kotlin.incremental.storage.ExternalizersKt$toDescriptor$1.save(externalizers.kt)
	at org.jetbrains.kotlin.com.intellij.util.io.keyStorage.AppendableStorageBackedByResizableMappedFile.checkBytesAreTheSame(AppendableStorageBackedByResizableMappedFile.java:165)
	at org.jetbrains.kotlin.com.intellij.util.io.PersistentEnumeratorBase.isKeyAtIndex(PersistentEnumeratorBase.java:370)
	at org.jetbrains.kotlin.com.intellij.util.io.PersistentBTreeEnumerator.enumerateImpl(PersistentBTreeEnumerator.java:528)
	at org.jetbrains.kotlin.com.intellij.util.io.PersistentEnumeratorBase.lambda$doEnumerate$0(PersistentEnumeratorBase.java:267)
	at org.jetbrains.kotlin.com.intellij.util.io.PersistentEnumeratorBase.catchCorruption(PersistentEnumeratorBase.java:608)
	... 36 more


