kotlin version: 2.0.0
error message: Incremental compilation failed: java.lang.ArrayIndexOutOfBoundsException: Array index out of range: 0
java.io.IOException: java.lang.ArrayIndexOutOfBoundsException: Array index out of range: 0
	at org.jetbrains.kotlin.com.intellij.util.io.CompressedAppendableFile.loadChunk(CompressedAppendableFile.java:185)
	at org.jetbrains.kotlin.com.intellij.util.io.CompressedAppendableFile.access$100(CompressedAppendableFile.java:30)
	at org.jetbrains.kotlin.com.intellij.util.io.CompressedAppendableFile$FileChunkReadCache.get(CompressedAppendableFile.java:464)
	at org.jetbrains.kotlin.com.intellij.util.io.CompressedAppendableFile$SegmentedChunkInputStream.read(CompressedAppendableFile.java:519)
	at java.base/java.io.DataInputStream.readFully(DataInputStream.java:201)
	at org.jetbrains.kotlin.com.intellij.util.io.PersistentHashMapValueStorage$ReaderOverCompressedFile.get(PersistentHashMapValueStorage.java:772)
	at org.jetbrains.kotlin.com.intellij.util.io.PersistentHashMapValueStorage.readBytes(PersistentHashMapValueStorage.java:542)
	at org.jetbrains.kotlin.com.intellij.util.io.PersistentMapImpl.doGet(PersistentMapImpl.java:604)
	at org.jetbrains.kotlin.com.intellij.util.io.PersistentMapImpl.get(PersistentMapImpl.java:545)
	at org.jetbrains.kotlin.com.intellij.util.io.PersistentHashMap.get(PersistentHashMap.java:178)
	at org.jetbrains.kotlin.incremental.storage.LazyStorage.get(LazyStorage.kt:74)
	at org.jetbrains.kotlin.incremental.storage.InMemoryStorage.get(InMemoryStorage.kt:68)
	at org.jetbrains.kotlin.incremental.storage.AppendableInMemoryStorage.get(InMemoryStorage.kt:151)
	at org.jetbrains.kotlin.incremental.storage.AppendableInMemoryStorage.get(InMemoryStorage.kt:143)
	at org.jetbrains.kotlin.incremental.storage.AppendableSetBasicMap.get(BasicMap.kt:137)
	at org.jetbrains.kotlin.incremental.storage.AbstractSourceToOutputMap.getFqNames(SourceToOutputMaps.kt:50)
	at org.jetbrains.kotlin.incremental.AbstractIncrementalCache.classesFqNamesBySources(AbstractIncrementalCache.kt:95)
	at org.jetbrains.kotlin.incremental.IncrementalCompilerRunner.getRemovedClassesChanges(IncrementalCompilerRunner.kt:622)
	at org.jetbrains.kotlin.incremental.IncrementalJvmCompilerRunner.calculateSourcesToCompileImpl(IncrementalJvmCompilerRunner.kt:263)
	at org.jetbrains.kotlin.incremental.IncrementalJvmCompilerRunner.calculateSourcesToCompile(IncrementalJvmCompilerRunner.kt:132)
	at org.jetbrains.kotlin.incremental.IncrementalJvmCompilerRunner.calculateSourcesToCompile(IncrementalJvmCompilerRunner.kt:62)
	at org.jetbrains.kotlin.incremental.IncrementalCompilerRunner.tryCompileIncrementally$lambda$9$compile(IncrementalCompilerRunner.kt:225)
	at org.jetbrains.kotlin.incremental.IncrementalCompilerRunner.tryCompileIncrementally(IncrementalCompilerRunner.kt:267)
	at org.jetbrains.kotlin.incremental.IncrementalCompilerRunner.compile(IncrementalCompilerRunner.kt:120)
	at org.jetbrains.kotlin.daemon.CompileServiceImplBase.execIncrementalCompiler(CompileServiceImpl.kt:676)
	at org.jetbrains.kotlin.daemon.CompileServiceImplBase.access$execIncrementalCompiler(CompileServiceImpl.kt:92)
	at org.jetbrains.kotlin.daemon.CompileServiceImpl.compile(CompileServiceImpl.kt:1661)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke0(Native Method)
	at java.base/jdk.internal.reflect.NativeMethodAccessorImpl.invoke(NativeMethodAccessorImpl.java:77)
	at java.base/jdk.internal.reflect.DelegatingMethodAccessorImpl.invoke(DelegatingMethodAccessorImpl.java:43)
	at java.base/java.lang.reflect.Method.invoke(Method.java:569)
	at java.rmi/sun.rmi.server.UnicastServerRef.dispatch(UnicastServerRef.java:360)
	at java.rmi/sun.rmi.transport.Transport$1.run(Transport.java:200)
	at java.rmi/sun.rmi.transport.Transport$1.run(Transport.java:197)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:712)
	at java.rmi/sun.rmi.transport.Transport.serviceCall(Transport.java:196)
	at java.rmi/sun.rmi.transport.tcp.TCPTransport.handleMessages(TCPTransport.java:587)
	at java.rmi/sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run0(TCPTransport.java:828)
	at java.rmi/sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.lambda$run$0(TCPTransport.java:705)
	at java.base/java.security.AccessController.doPrivileged(AccessController.java:399)
	at java.rmi/sun.rmi.transport.tcp.TCPTransport$ConnectionHandler.run(TCPTransport.java:704)
	at java.base/java.util.concurrent.ThreadPoolExecutor.runWorker(ThreadPoolExecutor.java:1136)
	at java.base/java.util.concurrent.ThreadPoolExecutor$Worker.run(ThreadPoolExecutor.java:635)
	at java.base/java.lang.Thread.run(Thread.java:840)
Caused by: java.lang.ArrayIndexOutOfBoundsException: Array index out of range: 0
	at org.jetbrains.kotlin.net.jpountz.util.SafeUtils.checkRange(SafeUtils.java:24)
	at org.jetbrains.kotlin.net.jpountz.util.UnsafeUtils.checkRange(UnsafeUtils.java:56)
	at org.jetbrains.kotlin.net.jpountz.lz4.LZ4JavaUnsafeFastDecompressor.decompress(LZ4JavaUnsafeFastDecompressor.java:23)
	at org.jetbrains.kotlin.net.jpountz.lz4.LZ4FastDecompressor.decompress(LZ4FastDecompressor.java:107)
	at org.jetbrains.kotlin.com.intellij.util.CompressionUtil.readCompressedWithoutOriginalBufferLength(CompressionUtil.java:93)
	at org.jetbrains.kotlin.com.intellij.util.io.CompressedAppendableFile.decompress(CompressedAppendableFile.java:353)
	at org.jetbrains.kotlin.com.intellij.util.io.CompressedAppendableFile.loadChunk(CompressedAppendableFile.java:173)
	... 43 more


