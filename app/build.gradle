plugins {
    id 'com.android.application'
    id 'kotlin-android'
    id 'kotlin-kapt'
    id 'com.google.dagger.hilt.android'
    id 'org.jetbrains.kotlin.plugin.compose'
    id 'com.google.gms.google-services'
    id 'com.google.firebase.crashlytics'
}

android {
    namespace = "com.thyview"
    compileSdk = 35

    defaultConfig {
        applicationId = "com.thyview"
        minSdk = 26
        targetSdk = 35
        versionCode = 1
        versionName = generateVersionName()
        testInstrumentationRunner = "androidx.test.runner.AndroidJUnitRunner"
    }

    signingConfigs {
        release {
            storeFile file('thyview-release')
            storePassword "MoviesStar2023"
            keyAlias "ThyviewRelease"
            keyPassword "MoviesStar2023**"
        }
    }

    buildFeatures {
        dataBinding = true
        viewBinding = true
        buildConfig = true
        compose = true
    }
    
    // Compose compiler configuration
    composeCompiler {
        enableStrongSkippingMode = true
    }
    
    kapt {
        correctErrorTypes = true
        // Add explicit Java version for annotation processing
        javacOptions {
            option("-source", "17")
            option("-target", "17")
        }
    }

    buildTypes {

        release {
            minifyEnabled false
            signingConfig signingConfigs.release
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'

            // Generate native symbols that gets uploaded to playstore
            ndk {
                debugSymbolLevel = 'FULL'
            }

            ndkVersion "22.0.7026061"
        }
        debug {
            versionNameSuffix "-SNAPSHOT"
            debuggable true
            minifyEnabled false
            shrinkResources false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'

            // Generate native symbols that gets uploaded to playstore
            ndk {
                debugSymbolLevel = 'FULL'
            }

            ndkVersion "22.0.7026061"
        }
    }

    compileOptions {
        sourceCompatibility = JavaVersion.VERSION_17
        targetCompatibility = JavaVersion.VERSION_17
    }
    kotlinOptions {
        jvmTarget = "17"
    }
}

private static String generateVersionName() {
    Integer versionMajor = 1
    Integer versionMinor = 0
    Integer versionPatch = 0
    Integer buildNumber = 0

    return versionMajor + "." + versionMinor + "." + versionPatch + "(" + buildNumber + ")"
}

dependencies {
    // Jar libs
    implementation fileTree(dir: 'libs', include: ['*.jar'])

    // compose
    def composeBom = platform('androidx.compose:compose-bom:2025.04.01')
    implementation composeBom
    androidTestImplementation composeBom
    implementation 'androidx.compose.material3:material3'
    implementation 'androidx.compose.foundation:foundation'

    implementation 'androidx.compose.material:material-icons-core'
    // Optional - Add full set of material icons
    implementation 'androidx.compose.material:material-icons-extended'

    // Optional - Integration with activities
    implementation 'androidx.activity:activity-compose:1.10.1'
    // Optional - Integration with ViewModels
    implementation 'androidx.lifecycle:lifecycle-viewmodel-compose:2.8.7'
    // Optional - Integration with LiveData
    implementation 'androidx.compose.runtime:runtime-livedata'
    
    // Add Compose UI tooling for previews
    implementation 'androidx.compose.ui:ui-tooling-preview'
    debugImplementation 'androidx.compose.ui:ui-tooling'
    implementation 'androidx.compose.material:material'


    // AndroidX Core Libraries
    implementation 'androidx.core:core-ktx:1.16.0'
    implementation 'androidx.appcompat:appcompat:1.7.0'
    implementation 'androidx.annotation:annotation:1.9.1'
    implementation 'androidx.legacy:legacy-support-v4:1.0.0'

    // UI Components
    implementation 'androidx.recyclerview:recyclerview:1.4.0'
    implementation 'androidx.constraintlayout:constraintlayout:2.2.1'
    implementation 'com.google.android.material:material:1.12.0'
    implementation "com.google.accompanist:accompanist-swiperefresh:0.30.1"

    // Navigation Components
    implementation 'androidx.navigation:navigation-ui-ktx:2.8.9'
    implementation 'androidx.navigation:navigation-fragment-ktx:2.8.9'

    // Lifecycle Components
    implementation 'androidx.lifecycle:lifecycle-livedata-ktx:2.8.7'
    implementation 'androidx.lifecycle:lifecycle-viewmodel-ktx:2.8.7'
    implementation 'androidx.lifecycle:lifecycle-extensions:2.2.0'

    implementation 'io.reactivex.rxjava3:rxandroid:3.0.2'

    // Room Database
    def room_version = "2.7.0"
    implementation "androidx.room:room-runtime:$room_version"
    implementation "androidx.room:room-ktx:$room_version"
    implementation "androidx.room:room-rxjava2:$room_version"
    kapt "androidx.room:room-compiler:$room_version"

    // Coroutines
    implementation 'org.jetbrains.kotlinx:kotlinx-coroutines-android:1.8.0'
    
    // WorkManager
    implementation "androidx.work:work-runtime-ktx:2.10.0"

    // Play Updates
    implementation 'com.google.android.play:app-update:2.1.0'

    implementation 'io.coil-kt:coil-compose:2.5.0'


    // Glide
    def glide_version = "4.16.0"
    implementation "com.github.bumptech.glide:glide:$glide_version"
    kapt "com.github.bumptech.glide:compiler:$glide_version"

    // Lottie
    implementation 'com.airbnb.android:lottie:6.3.0'

    // MultiSlider
    implementation 'io.apptik.widget:multislider:1.3'

    // Location Services
    implementation 'com.google.android.gms:play-services-location:21.3.0'

    // Timber
    implementation 'com.jakewharton.timber:timber:5.0.1'

    // Media3 Player
    def media3_version = "1.2.1"
    implementation "androidx.media3:media3-exoplayer:$media3_version"
    implementation "androidx.media3:media3-ui:$media3_version"

    // Konfetti
    implementation 'nl.dionsegijn:konfetti:1.3.2'

    // Google Play Billing
    implementation "com.android.billingclient:billing-ktx:7.1.1"

    // Gson
    implementation 'com.google.code.gson:gson:2.10.1'

    // ExifInterface
    implementation 'androidx.exifinterface:exifinterface:1.4.0'

    // Firebase BoM
    implementation platform('com.google.firebase:firebase-bom:33.12.0')
    
    // Firebase Dependencies
    implementation 'com.google.firebase:firebase-analytics-ktx'
    implementation 'com.google.firebase:firebase-functions-ktx'
    implementation 'com.google.firebase:firebase-dynamic-links-ktx'
    implementation 'com.google.firebase:firebase-perf-ktx'
    implementation 'com.google.firebase:firebase-inappmessaging-display-ktx'
    implementation 'com.google.firebase:firebase-auth-ktx'
    implementation 'com.google.firebase:firebase-database-ktx'
    implementation 'com.google.firebase:firebase-messaging:24.1.1'
    implementation 'com.google.firebase:firebase-crashlytics-ktx'
    implementation 'com.google.firebase:firebase-config-ktx'
    implementation 'com.google.firebase:firebase-firestore-ktx'

    // EasyImage
    implementation 'com.github.jkwiecien:EasyImage:3.2.0'

    // Places
    implementation 'com.google.android.libraries.places:places:4.2.0'

    // uCrop
    implementation 'com.github.yalantis:ucrop:2.2.10'

    // Branch.io
    implementation 'io.branch.sdk.android:library:5.16.2'
    implementation 'com.google.android.gms:play-services-ads-identifier:18.2.0'

    // Play Services
    implementation 'com.google.android.gms:play-services-maps:19.2.0'
    implementation 'com.google.android.gms:play-services-auth:21.3.0'

    // Facebook
    implementation 'com.facebook.android:facebook-login:18.0.3'

    // Shimmer
    implementation 'com.facebook.shimmer:shimmer:0.5.0'

    // Mixpanel
    implementation "com.mixpanel.android:mixpanel-android:8.0.3"

    // Retrofit & OkHttp
    def retrofit_version = "2.9.0"
    implementation "com.squareup.retrofit2:retrofit:$retrofit_version"
    implementation "com.squareup.retrofit2:adapter-rxjava3:$retrofit_version"
    implementation "com.squareup.retrofit2:converter-gson:$retrofit_version"
    implementation "com.squareup.retrofit2:converter-scalars:$retrofit_version"
    implementation "com.squareup.okhttp3:logging-interceptor:4.12.0"

    // Apollo
    def apollo_version = "2.5.14"
    implementation "com.apollographql.apollo:apollo-runtime:$apollo_version"
    implementation "com.apollographql.apollo:apollo-rx2-support:$apollo_version"
    implementation "com.apollographql.apollo:apollo-coroutines-support:$apollo_version"

    // Stetho
    implementation 'com.facebook.stetho:stetho:1.6.0'
    implementation 'com.facebook.stetho:stetho-okhttp3:1.6.0'

    // Epoxy
    def epoxy_version = "5.1.3"
    implementation "com.airbnb.android:epoxy:$epoxy_version"
    implementation "com.airbnb.android:epoxy-databinding:$epoxy_version"
    kapt "com.airbnb.android:epoxy-processor:$epoxy_version"

    // Hilt
    def hilt_version = "2.51.1"
    implementation "com.google.dagger:hilt-android:$hilt_version"
    kapt "com.google.dagger:hilt-compiler:$hilt_version"

    implementation "androidx.hilt:hilt-navigation-compose:1.2.0"

    // Testing Dependencies
    testImplementation 'junit:junit:4.13.2'
    testImplementation 'org.mockito.kotlin:mockito-kotlin:5.2.1'
    testImplementation 'org.jetbrains.kotlinx:kotlinx-coroutines-test:1.8.0'
    testImplementation "com.google.dagger:hilt-android-testing:$hilt_version"
    kaptTest "com.google.dagger:hilt-compiler:$hilt_version"

    // Android Testing
    androidTestImplementation 'androidx.test:runner:1.6.2'
    androidTestImplementation 'androidx.test:rules:1.6.1'
    androidTestImplementation 'androidx.test.espresso:espresso-core:3.6.1'
    androidTestImplementation "androidx.test.uiautomator:uiautomator:2.3.0"
    androidTestImplementation "com.google.dagger:hilt-android-testing:$hilt_version"
    kaptAndroidTest "com.google.dagger:hilt-compiler:$hilt_version"
    androidTestImplementation 'androidx.compose.ui:ui-test-junit4'
    debugImplementation 'androidx.compose.ui:ui-test-manifest'
}
