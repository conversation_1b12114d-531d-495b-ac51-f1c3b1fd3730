package com.thyview.services

import com.google.gson.Gson
import kotlinx.coroutines.test.runTest
import org.junit.Before
import org.junit.Test
import org.junit.Assert.*
import org.mockito.Mock
import org.mockito.MockitoAnnotations

/**
 * Unit tests for SearchService mock data functionality
 */
class SearchServiceTest {

    @Mock
    private lateinit var mockSearchApiService: SearchApiService

    private lateinit var searchService: SearchService
    private lateinit var gson: Gson

    @Before
    fun setup() {
        MockitoAnnotations.openMocks(this)
        gson = Gson()
        searchService = SearchService(mockSearchApiService, gson)
    }

    @Test
    fun `search with avengers query returns mock data`() = runTest {
        // When
        val result = searchService.search("avengers")

        // Then
        assertTrue("Search should succeed", result.isSuccess)
        val response = result.getOrNull()
        assertNotNull("Response should not be null", response)
        assertEquals("Query should match", "avengers", response?.query)
        assertEquals("Should return 2 results", 2, response?.results?.size)
        
        // Verify first result is The Avengers
        val firstResult = response?.results?.get(0)
        assertEquals("First result should be The Avengers", "The Avengers", firstResult?.title)
        assertEquals("Should be movie type", "movie", firstResult?.type)
        assertEquals("Should have correct ID", 24428, firstResult?.id)
    }

    @Test
    fun `search with stranger things query returns TV show`() = runTest {
        // When
        val result = searchService.search("stranger things")

        // Then
        assertTrue("Search should succeed", result.isSuccess)
        val response = result.getOrNull()
        assertNotNull("Response should not be null", response)
        
        val firstResult = response?.results?.get(0)
        assertEquals("Should be Stranger Things", "Stranger Things", firstResult?.name)
        assertEquals("Should be TV type", "tv", firstResult?.type)
        assertEquals("Should have correct ID", 66732, firstResult?.id)
    }

    @Test
    fun `search with leonardo dicaprio query returns person`() = runTest {
        // When
        val result = searchService.search("leonardo dicaprio")

        // Then
        assertTrue("Search should succeed", result.isSuccess)
        val response = result.getOrNull()
        assertNotNull("Response should not be null", response)
        
        val firstResult = response?.results?.get(0)
        assertEquals("Should be Leonardo DiCaprio", "Leonardo DiCaprio", firstResult?.name)
        assertEquals("Should be person type", "person", firstResult?.type)
        assertEquals("Should have correct ID", 6193, firstResult?.id)
    }

    @Test
    fun `search with unknown query returns empty results`() = runTest {
        // When
        val result = searchService.search("unknown movie that does not exist")

        // Then
        assertTrue("Search should succeed", result.isSuccess)
        val response = result.getOrNull()
        assertNotNull("Response should not be null", response)
        assertEquals("Should return empty results", 0, response?.results?.size)
    }

    @Test
    fun `getMovieDetails returns correct mock data`() = runTest {
        // When
        val result = searchService.getMovieDetails(24428) // The Avengers

        // Then
        assertTrue("Should succeed", result.isSuccess)
        val movieDetails = result.getOrNull()
        assertNotNull("Movie details should not be null", movieDetails)
        assertEquals("Should be The Avengers", "The Avengers", movieDetails?.title)
        assertEquals("Should have correct year", 2012, movieDetails?.year)
        assertEquals("Should have correct runtime", 143, movieDetails?.runtime)
        assertTrue("Should have cast members", movieDetails?.cast?.isNotEmpty() == true)
    }

    @Test
    fun `getTVDetails returns correct mock data`() = runTest {
        // When
        val result = searchService.getTVDetails(66732) // Stranger Things

        // Then
        assertTrue("Should succeed", result.isSuccess)
        val tvDetails = result.getOrNull()
        assertNotNull("TV details should not be null", tvDetails)
        assertEquals("Should be Stranger Things", "Stranger Things", tvDetails?.name)
        assertEquals("Should have correct year", 2016, tvDetails?.releaseYear)
        assertEquals("Should have 4 seasons", 4, tvDetails?.seasons)
        assertTrue("Should have cast members", tvDetails?.cast?.isNotEmpty() == true)
    }

    @Test
    fun `getPersonDetails returns correct mock data`() = runTest {
        // When
        val result = searchService.getPersonDetails(6193) // Leonardo DiCaprio

        // Then
        assertTrue("Should succeed", result.isSuccess)
        val personDetails = result.getOrNull()
        assertNotNull("Person details should not be null", personDetails)
        assertEquals("Should be Leonardo DiCaprio", "Leonardo DiCaprio", personDetails?.name)
        assertEquals("Should be actor", "Acting", personDetails?.knownForDepartment)
        assertTrue("Should have movie credits", personDetails?.movieCredits?.cast?.isNotEmpty() == true)
    }

    @Test
    fun `getStreamingServices returns mock services`() = runTest {
        // When
        val result = searchService.getStreamingServices("movie", 24428)

        // Then
        assertTrue("Should succeed", result.isSuccess)
        val services = result.getOrNull()
        assertNotNull("Services should not be null", services)
        assertEquals("Should return 5 services", 5, services?.size)
        
        // Verify Netflix is included
        val netflix = services?.find { it.name == "Netflix" }
        assertNotNull("Netflix should be included", netflix)
        assertEquals("Netflix should have correct ID", 1, netflix?.id)
    }

    @Test
    fun `toggleWatchlist returns success response`() = runTest {
        // When - adding to watchlist
        val result = searchService.toggleWatchlist(24428, "movie", false)

        // Then
        assertTrue("Should succeed", result.isSuccess)
        val response = result.getOrNull()
        assertNotNull("Response should not be null", response)
        assertTrue("Should be successful", response?.success == true)
        assertTrue("Should be in watchlist", response?.isInWatchlist == true)
        assertEquals("Should have correct message", "Added to watchlist", response?.message)
    }

    @Test
    fun `isInWatchlist returns consistent results`() = runTest {
        // When - checking items divisible by 3 (should be in watchlist)
        val result1 = searchService.isInWatchlist("movie", 24) // 24 % 3 == 0
        val result2 = searchService.isInWatchlist("movie", 25) // 25 % 3 != 0

        // Then
        assertTrue("Should succeed for item 24", result1.isSuccess)
        assertTrue("Should succeed for item 25", result2.isSuccess)
        assertTrue("Item 24 should be in watchlist", result1.getOrNull() == true)
        assertFalse("Item 25 should not be in watchlist", result2.getOrNull() == true)
    }

    @Test
    fun `search with partial match works correctly`() = runTest {
        // When - searching with partial query
        val result = searchService.search("aven") // Should match "avengers"

        // Then
        assertTrue("Search should succeed", result.isSuccess)
        val response = result.getOrNull()
        assertNotNull("Response should not be null", response)
        assertTrue("Should return results", response?.results?.isNotEmpty() == true)
        
        val firstResult = response?.results?.get(0)
        assertEquals("Should find The Avengers", "The Avengers", firstResult?.title)
    }
}
