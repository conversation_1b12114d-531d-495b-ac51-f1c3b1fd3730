<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Project level resources -->
    <style name="AppTheme" parent="Theme.MaterialComponents.Light.DarkActionBar">
        <item name="colorPrimary">@color/color_primary</item>
        <item name="colorPrimaryDark">@color/color_primary</item>
        <item name="colorAccent">@color/color_secondary</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowDisablePreview">true</item>
        <item name="android:windowIsTranslucent">true</item>
<!--        <item name="android:statusBarColor">@color/color_background</item>-->
        <item name="android:windowLightStatusBar">true</item>
        <item name="android:fitsSystemWindows">true</item>
        <item name="android:windowDrawsSystemBarBackgrounds">true</item>
    </style>

    <style name="BottomCTAButton">
        <item name="android:fontFamily">@font/inter_bold</item>
        <item name="android:textSize">18sp</item>
        <item name="android:lineSpacingMultiplier">1</item>
        <item name="android:gravity">center</item>
        <item name="android:textAllCaps">false</item>
    </style>
</resources>