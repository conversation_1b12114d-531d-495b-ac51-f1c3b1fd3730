<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:padding="8dp">

    <!-- Title TextView: "User Reviews" -->
    <TextView
        android:id="@+id/userReviewsTitleTextView"
        style="@style/BoldFont"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="User Reviews"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- Title TextView: "Rating" -->
    <TextView
        android:id="@+id/ratingTitleTextView"
        style="@style/RegularFont"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="6dp"
        android:text="Thyview Rating"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/userReviewsTitleTextView" />

    <!-- Star Icon -->
    <ImageView
        android:id="@+id/starIcon"
        android:layout_width="36dp"
        android:layout_height="36dp"
        android:layout_marginStart="8dp"
        android:layout_marginTop="6dp"
        android:src="@drawable/ic_star"
        app:layout_constraintStart_toStartOf="@+id/yourRatingTextView"
        app:layout_constraintTop_toBottomOf="@+id/yourRatingTextView" />

    <!-- Number "7/10" -->
    <TextView
        android:id="@+id/ratingNumberTextView"
        style="@style/RegularFont"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:layout_marginTop="8dp"
        android:text="7/10"
        app:layout_constraintStart_toEndOf="@+id/starIcon"
        app:layout_constraintTop_toTopOf="@+id/starIcon" />

    <!-- "Your Rating" TextView -->
    <TextView
        android:id="@+id/yourRatingTextView"
        style="@style/RegularFont"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="6dp"
        android:layout_marginEnd="16dp"
        android:text="Your Rating"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toEndOf="@+id/ratingTitleTextView"
        app:layout_constraintTop_toBottomOf="@+id/userReviewsTitleTextView" />

    <!-- Star Icon for Your Rating -->
    <ImageView
        android:id="@+id/starIconYourRating"
        android:layout_width="36dp"
        android:layout_height="36dp"
        android:layout_marginStart="16dp"
        android:layout_marginTop="6dp"
        android:src="@drawable/ic_star"
        app:layout_constraintStart_toStartOf="@+id/ratingTitleTextView"
        app:layout_constraintTop_toBottomOf="@+id/yourRatingTextView" />

    <!-- Number "8/10" for Your Rating -->
    <TextView
        android:id="@+id/ratingNumberYourRatingTextView"
        style="@style/RegularFont"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="8dp"
        android:layout_marginTop="8dp"
        android:text="8/10"
        app:layout_constraintStart_toEndOf="@+id/starIconYourRating"
        app:layout_constraintTop_toTopOf="@+id/starIconYourRating" />

    <!-- "Add Review" Button -->
    <Button
        android:id="@+id/addReviewButton"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="6dp"
        android:text="Add Review"
        android:textAllCaps="false"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/starIconYourRating" />

    <!-- Line below "Add Review" Button -->
    <View
        android:id="@+id/lineBelowButton"
        android:layout_width="0dp"
        android:layout_height="1dp"
        android:layout_marginTop="4dp"
        android:background="@color/grey4"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/addReviewButton" />

    <!-- "See All Reviews" TextView -->
    <TextView
        android:id="@+id/seeAllReviewsTextView"
        style="@style/RegularFont"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:text="See All Reviews"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/lineBelowButton" />

    <!-- Chevron Icon -->
    <ImageView
        android:id="@+id/chevronIcon"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="16dp"
        android:src="@drawable/ic_arrow_right"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/lineBelowButton" />

    <!-- Line below "See All Reviews" TextView -->
    <View
        android:id="@+id/lineBelowSeeAllReviews"
        android:layout_width="0dp"
        android:layout_height="1dp"
        android:layout_marginTop="8dp"
        android:background="@color/grey4"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/seeAllReviewsTextView" />

</androidx.constraintlayout.widget.ConstraintLayout>
