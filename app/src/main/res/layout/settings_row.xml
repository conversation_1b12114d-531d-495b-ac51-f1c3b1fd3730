<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="72dp"
    android:background="@color/white">

    <TextView
        android:id="@+id/your_favorite_hobby_text_view"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="@dimen/container_horizontal_margin_27"
        android:fontFamily="@font/inter"
        android:gravity="start"
        android:lineSpacingMultiplier="1.09"
        android:text="@string/settings_main_item_1"
        android:textColor="@color/grey2"
        android:textSize="@dimen/medium_text_size_18"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:layout_editor_absoluteX="24dp"
        tools:layout_editor_absoluteY="18dp"/>

    <ImageView
        android:id="@+id/stre_right_image_view"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="@dimen/activity_horizontal_margin_16"
        android:scaleType="center"
        android:src="@drawable/ic_arrow_right"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:layout_editor_absoluteX="299dp"
        tools:layout_editor_absoluteY="24dp"/>
</androidx.constraintlayout.widget.ConstraintLayout>