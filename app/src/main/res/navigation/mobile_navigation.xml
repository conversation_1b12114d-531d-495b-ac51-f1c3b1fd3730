<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/mobile_navigation"
    app:startDestination="@+id/navigation_home">

    <fragment
        android:id="@+id/navigation_home"
        android:name="com.thyview.ui.home.HomeFragment"
        android:label="@string/title_home"
        tools:layout="@layout/fragment_home" />

    <fragment
        android:id="@+id/navigation_articles"
        android:name="com.thyview.ui.articles.ArticlesFragment"
        android:label="@string/title_articles"
        tools:layout="@layout/fragment_dashboard" />

    <fragment
        android:id="@+id/navigation_search"
        android:name="com.thyview.ui.search.SearchFragment"
        android:label="@string/title_search"
        tools:layout="@layout/fragment_search" />

    <fragment
        android:id="@+id/navigation_reviews"
        android:name="com.thyview.ui.reviews.ReviewsFragment"
        android:label="@string/title_reviews"
        tools:layout="@layout/fragment_reviews">
        <action
            android:id="@+id/action_navigation_reviews_to_postDetailScreen"
            app:destination="@id/navigation_post_detail" />
    </fragment>

    <fragment
        android:id="@+id/navigation_profile"
        android:name="com.thyview.ui.profile.ProfileFragment"
        android:label="@string/title_profile"
        tools:layout="@layout/fragment_profile" />
        
    <fragment
        android:id="@+id/navigation_post_detail"
        android:name="com.thyview.ui.reviews.PostDetailFragment"
        android:label="Post Details"
        tools:layout="@layout/fragment_post_detail">
        <argument
            android:name="postId"
            app:argType="string"
            app:nullable="false" />
    </fragment>
</navigation>
