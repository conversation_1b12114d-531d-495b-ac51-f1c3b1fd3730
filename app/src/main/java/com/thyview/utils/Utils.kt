package com.thyview.utils

import android.app.Activity
import android.content.Context
import android.content.Intent
import android.net.Uri
import android.provider.Settings
import android.text.Editable
import android.text.TextUtils
import android.text.TextWatcher
import android.util.Patterns
import android.widget.EditText
import androidx.appcompat.app.AlertDialog
import com.thyview.storage.prefs.AccountPreferences
import timber.log.Timber
import java.text.DateFormat
import java.text.ParseException
import java.text.ParsePosition
import java.text.SimpleDateFormat
import java.util.Calendar
import java.util.Date
import java.util.Locale
import kotlin.math.roundToLong

class Utils {

    companion object {

        fun isValidEmail(email: String): Boolean = email.isNotEmpty() &&
                Patterns.EMAIL_ADDRESS.matcher(email).matches()

        fun isValidPhone(phone: String): Boolean = phone.isNotEmpty() &&
                Patterns.PHONE.matcher(phone).matches()

        fun isValidString(string: String): Boolean = !TextUtils.isEmpty(string)

        private fun roundToHalf(d: Double): Double {

            return (d * 2).roundToLong() / 2.0
        }

        fun EditText.afterTextChanged(afterTextChanged: (String) -> Unit) {
            this.addTextChangedListener(object : TextWatcher {

                override fun afterTextChanged(s: Editable?) {
                    afterTextChanged.invoke(s.toString())
                }

                override fun beforeTextChanged(
                    s: CharSequence?,
                    start: Int,
                    count: Int,
                    after: Int
                ) {
                }

                override fun onTextChanged(s: CharSequence?, start: Int, before: Int, count: Int) {}
            })
        }

        fun EditText.validate(message: String, validator: (String) -> Boolean) {
            this.afterTextChanged {
                validator(it)
                //this.error = if (validator(it)) null else message
            }
            //this.error = if (validator(this.text.toString())) null else message
        }

        fun isUserAgerestricted(dob: String): Boolean {

            if (dob.isEmpty()) {

                return false
            }

            try {

                val userFormattedDob = dob.replace('/', '-')
                val sdf = SimpleDateFormat("MM-dd-yyyy", Locale.US)
                val dobCalender = Calendar.getInstance()
                dobCalender.time = sdf.parse(userFormattedDob)

                if (!isUser18YearsOld(dobCalender)) {

                    return true
                }
            } catch (exception: java.lang.Exception) {

                Timber.d("exception in isUserAgerestricted: $exception")
            }

            return false
        }

        fun isValidDate(dateString: String): Boolean {

            try {

                val sdf = SimpleDateFormat("yyyy-MM-dd", Locale.US)
                sdf.isLenient = false
                return sdf.parse(dateString, ParsePosition(0)) != null

            } catch (exception: java.lang.Exception) {

                Timber.e("exception in isValidDate: $exception")
            }

            return false
        }

        fun isUser18YearsOld(dob: Calendar): Boolean {

            try {

                val today = Calendar.getInstance()

                val curYear = today.get(Calendar.YEAR)
                val dobYear = dob.get(Calendar.YEAR)

                var age = curYear - dobYear

                // if dob is month or day is behind today's month or day
                // reduce age by 1
                val curMonth = today.get(Calendar.MONTH)
                val dobMonth = dob.get(Calendar.MONTH)
                if (dobMonth > curMonth) { // this year can't be counted!
                    age--
                } else if (dobMonth == curMonth) { // same month? check for day
                    val curDay = today.get(Calendar.DAY_OF_MONTH)
                    val dobDay = dob.get(Calendar.DAY_OF_MONTH)
                    if (dobDay > curDay) { // this year can't be counted!
                        age--
                    }
                }

                if (age < 18) {

                    return false
                }

            } catch (exception: java.lang.Exception) {

                Timber.e("exception in isUser18YearsOld: $exception")
            }

            return true
        }

        fun getAge(dateOfBirth: Date?): Int {
            var age = 0
            try {
                val born = Calendar.getInstance()
                val now = Calendar.getInstance()
                if (dateOfBirth != null) {
                    now.time = Date()
                    born.time = dateOfBirth
                    if (born.after(now)) {
                        throw IllegalArgumentException("Can't be born in the future")
                    }
                    age = now.get(Calendar.YEAR) - born.get(Calendar.YEAR)
                    if (now.get(Calendar.DAY_OF_YEAR) < born.get(Calendar.DAY_OF_YEAR)) {
                        age -= 1
                    }
                }
            } catch (exception: java.lang.Exception) {
                Timber.e("exception in getting age: $exception")
            }
            return age
        }

        fun isThisWeek(date: Date): Boolean {

            val c = Calendar.getInstance()
            c.firstDayOfWeek = Calendar.MONDAY

            c.set(Calendar.DAY_OF_WEEK, Calendar.MONDAY)
            c.set(Calendar.HOUR_OF_DAY, 0)
            c.set(Calendar.MINUTE, 0)
            c.set(Calendar.SECOND, 0)
            c.set(Calendar.MILLISECOND, 0)

            val monday = c.time

            val nextMonday = Date(monday.time + 7 * 24 * 60 * 60 * 1000)

            return date.after(monday) && date.before(nextMonday)
        }

        fun getDateFromTimestamp(time: Long): Date {
            val cal = Calendar.getInstance()
            val tz = cal.timeZone//get your local time zone.
            val sdf = SimpleDateFormat("dd/MM/yyyy hh:mm a", Locale.US)
            sdf.timeZone = tz//set time zone.
            val localTime = sdf.format(Date(time))
            var date = Date()
            try {
                date = sdf.parse(localTime)//get local date
            } catch (e: ParseException) {
                Timber.e("exception in get date from timestamp: $e")
            }
            return date
        }

        fun getDeviceDate(): String {
            val dateFormat: DateFormat = SimpleDateFormat("MM-dd-yyyy", Locale.US)
            return dateFormat.format(Calendar.getInstance().time)
        }

        fun extractDateStringFromDateObject(date: Date): String {
            val dateFormat: DateFormat = SimpleDateFormat("MM-dd-yyyy", Locale.US)
            return dateFormat.format(date)
        }

        fun openGmailApp(context: Context, subject: String) {
            try {
                val externalId = AccountPreferences.getInstance(context)
                    .getStringValue(Constants.externalId, "")
                val intent = Intent(Intent.ACTION_VIEW)
                intent.data = Uri.parse("mailto:${Constants.adminEmail}?subject=" + subject + "&body=\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n***********************\nDevice: " + externalId)
                context.startActivity(intent)
            } catch (e: Exception) {
                Timber.d("error opening gmail app: $e")
            }
        }

        fun getKmsFromMeters(meters: Int): Int {

            return (meters * 0.001).toInt()
        }

//        fun setDeviceHardwareIdentifier(context: Context) {
//            FirebaseInstallations.getInstance().id.addOnCompleteListener { task ->
//                if (task.isSuccessful) {
//                    val firebaseDatabaseReference = FirebaseDatabaseUtil(context)
//                    firebaseDatabaseReference.saveUserInfoToFirebase(Constants.deviceHardwareIdentifier, task.result)
//                }
//            }
//        }

        fun showAlert(text: String, activity: Context) {
            if (activity is Activity &&
                !activity.isFinishing
            ) {

                val dialogBuilder = AlertDialog.Builder(activity)

                dialogBuilder.setMessage(text)
                    .setCancelable(false)
                    .setPositiveButton("ok") { dialog, id ->

                        dialog.dismiss()
                    }

                val alert = dialogBuilder.create()
                alert.show()
            }
        }

        fun showPermissionAlert(text: String, activity: Context) {
            if (activity is Activity &&
                !activity.isFinishing
            ) {

                val dialogBuilder = AlertDialog.Builder(activity)

                dialogBuilder.setMessage(text)
                    .setCancelable(false)
                    .setPositiveButton("Go to settings") { dialog, id ->
                        activity.startActivity(Intent().apply {
                            action = Settings.ACTION_APPLICATION_DETAILS_SETTINGS
                            data = Uri.fromParts("package", activity.packageName, null)
                        })
                    }
                    .setNegativeButton("cancel") { dialog, id ->
                        dialog.dismiss()
                    }

                val alert = dialogBuilder.create()
                alert.show()
            }
        }
    }
}
