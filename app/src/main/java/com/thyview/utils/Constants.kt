package com.thyview.utils

object Constants {
    const val externalId = "externalId"
    //TODO: Change email
    const val adminEmail = "<EMAIL>"

    const val userUID = "userUID"

    // influencer promotions
    const val influencerSignups = "influencerSignups"
    const val influencerId = "influencerId"
    const val influencerName = "influencerName"
    const val freeTrailUsed = "freeTrailUsed"
    const val influencerPromoShown = "influencerPromoShown"
    const val promoCounter = "promoCounter"

    // Branch
    const val source = "source"
    const val installSource = "installSource"

    const val alerts = "alerts"
    const val offers = "offers"

    const val userEmail = "email"
}