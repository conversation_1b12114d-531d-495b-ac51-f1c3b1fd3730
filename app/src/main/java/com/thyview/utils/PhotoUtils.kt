package com.thyview.utils

import android.content.Context
import android.graphics.Bitmap
import android.graphics.BitmapFactory
import android.graphics.Matrix
import android.net.Uri
import androidx.exifinterface.media.ExifInterface
import timber.log.Timber
import java.io.FileOutputStream
import java.io.IOException

class PhotoUtils {

    companion object {

        fun handleImageOrientation(context: Context, currentPhotoPath: String?, uri: Uri? = null): Bitmap? {
            val bitmap = BitmapFactory.decodeFile(currentPhotoPath)
            var rotatedBitmap: Bitmap? = null
            try {
                var ei: ExifInterface? = null
                if (currentPhotoPath != null) {
                    ei = ExifInterface(currentPhotoPath)
                }else if (uri != null) {
                    val parcelFileDescriptor = context.contentResolver.openAssetFileDescriptor(uri, "r")
                    val fileDescriptor = parcelFileDescriptor?.fileDescriptor
                    ei = fileDescriptor?.let { ExifInterface(it) }
                }

                val orientation: Int? = ei?.getAttributeInt(ExifInterface.TAG_ORIENTATION, ExifInterface.ORIENTATION_UNDEFINED)
                rotatedBitmap = when (orientation) {
                    ExifInterface.ORIENTATION_ROTATE_90 -> rotateImage(bitmap, 90f)
                    ExifInterface.ORIENTATION_ROTATE_180 -> rotateImage(bitmap, 180f)
                    ExifInterface.ORIENTATION_ROTATE_270 -> rotateImage(bitmap, 270f)
                    ExifInterface.ORIENTATION_NORMAL -> bitmap
                    else -> bitmap
                }
                if (rotatedBitmap != bitmap) {
                    val fOut = FileOutputStream(currentPhotoPath)
                    rotatedBitmap?.compress(Bitmap.CompressFormat.JPEG, 100, fOut)
                    fOut.flush()
                    fOut.close()
                }
            } catch (e: IOException) {
                Timber.e("Exception in handleImageOrientation $e")
            }

            return rotatedBitmap
        }

        private fun rotateImage(source: Bitmap, angle: Float): Bitmap {
            val matrix = Matrix()
            matrix.postRotate(angle)
            return Bitmap.createBitmap(source, 0, 0, source.width, source.height, matrix, true)
        }
    }
}