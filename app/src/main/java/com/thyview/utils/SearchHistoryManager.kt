package com.thyview.utils

import android.content.Context
import android.content.SharedPreferences
import com.google.gson.Gson
import com.google.gson.reflect.TypeToken
import dagger.hilt.android.qualifiers.ApplicationContext
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Manages search history locally using SharedPreferences
 * Maintains a maximum of 10 recent searches
 */
@Singleton
class SearchHistoryManager @Inject constructor(
    @ApplicationContext private val context: Context,
    private val gson: Gson
) {
    
    private val prefs: SharedPreferences = context.getSharedPreferences(PREFS_NAME, Context.MODE_PRIVATE)
    
    companion object {
        private const val PREFS_NAME = "search_history"
        private const val KEY_SEARCH_HISTORY = "search_queries"
        private const val MAX_HISTORY_SIZE = 10
    }
    
    /**
     * Add a search query to history
     */
    fun addSearchQuery(query: String) {
        if (query.isBlank() || query.length < 3) return
        
        try {
            val currentHistory = getSearchHistory().toMutableList()
            
            // Remove if already exists to avoid duplicates
            currentHistory.remove(query)
            
            // Add to the beginning
            currentHistory.add(0, query)
            
            // Keep only the most recent MAX_HISTORY_SIZE items
            if (currentHistory.size > MAX_HISTORY_SIZE) {
                currentHistory.removeAt(currentHistory.size - 1)
            }
            
            // Save back to preferences
            val json = gson.toJson(currentHistory)
            prefs.edit().putString(KEY_SEARCH_HISTORY, json).apply()
            
            Timber.d("Added search query to history: $query")
        } catch (e: Exception) {
            Timber.e(e, "Error adding search query to history: $query")
        }
    }
    
    /**
     * Get search history list
     */
    fun getSearchHistory(): List<String> {
        return try {
            val json = prefs.getString(KEY_SEARCH_HISTORY, null)
            if (json != null) {
                val type = object : TypeToken<List<String>>() {}.type
                gson.fromJson(json, type) ?: emptyList()
            } else {
                emptyList()
            }
        } catch (e: Exception) {
            Timber.e(e, "Error getting search history")
            emptyList()
        }
    }
    
    /**
     * Clear all search history
     */
    fun clearSearchHistory() {
        try {
            prefs.edit().remove(KEY_SEARCH_HISTORY).apply()
            Timber.d("Search history cleared")
        } catch (e: Exception) {
            Timber.e(e, "Error clearing search history")
        }
    }
    
    /**
     * Remove a specific search query from history
     */
    fun removeSearchQuery(query: String) {
        try {
            val currentHistory = getSearchHistory().toMutableList()
            if (currentHistory.remove(query)) {
                val json = gson.toJson(currentHistory)
                prefs.edit().putString(KEY_SEARCH_HISTORY, json).apply()
                Timber.d("Removed search query from history: $query")
            }
        } catch (e: Exception) {
            Timber.e(e, "Error removing search query from history: $query")
        }
    }
    
    /**
     * Get filtered search suggestions based on current input
     */
    fun getSearchSuggestions(currentInput: String): List<String> {
        if (currentInput.isBlank()) return getSearchHistory()
        
        return getSearchHistory().filter { 
            it.contains(currentInput, ignoreCase = true) && it != currentInput 
        }
    }
}
