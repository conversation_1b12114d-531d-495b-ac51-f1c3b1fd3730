package com.thyview.utils

import android.app.Activity
import android.content.DialogInterface
import android.text.Html
import android.text.method.LinkMovementMethod
import android.widget.TextView
import androidx.appcompat.app.AlertDialog

object AlertDialogView {

    fun showAlertDialog(context: Activity?,
                        title: String = "",
                        message: String,
                        buttonPositiveText: String?,
                        buttonNegativeText: String?,
                        callback: (dialog: DialogInterface, which: Int) -> Unit) {

        val buttonClickListener = DialogInterface.OnClickListener { dialog, which ->
            callback(dialog, which)
        }

        val dialogBuilder = context?.let { activityContext -> AlertDialog.Builder(activityContext) }
        dialogBuilder?.setTitle(title)
        dialogBuilder?.setMessage(message)

        if (buttonPositiveText != null) dialogBuilder?.setPositiveButton(buttonPositiveText, buttonClickListener)
        if (buttonNegativeText != null) dialogBuilder?.setNegativeButton(buttonNegativeText, buttonClickListener)

        val dialog = dialogBuilder?.create()
        dialog?.setCancelable(false)

        if (context != null && dialog != null && !context.isFinishing) {
            dialog.show()
        }
    }

    fun showAlertDialogWithLink(context: Activity?,
                        title: String = "",
                        message: String,
                        buttonPositiveText: String?,
                        buttonNegativeText: String?,
                        callback: (dialog: DialogInterface, which: Int) -> Unit) {

        val buttonClickListener = DialogInterface.OnClickListener { dialog, which ->

            callback(dialog, which)
        }

        val dialogBuilder = context?.let { activityContext -> AlertDialog.Builder(activityContext) }
        dialogBuilder?.setTitle(title)

        dialogBuilder?.setMessage(Html.fromHtml(message))

        if (buttonPositiveText != null) dialogBuilder?.setPositiveButton(buttonPositiveText, buttonClickListener)
        if (buttonNegativeText != null) dialogBuilder?.setNegativeButton(buttonNegativeText, buttonClickListener)

        val dialog = dialogBuilder?.create()
        dialog?.setCancelable(false)

        if (context != null && dialog != null && !context.isFinishing) {

            dialog.show()
            val dialogTextView = dialog.window?.findViewById(androidx.appcompat.R.id.message) as? TextView
            dialogTextView?.let {
                it.movementMethod = LinkMovementMethod.getInstance()
            }
        }
    }
}