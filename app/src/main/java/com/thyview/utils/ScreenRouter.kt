package com.thyview.utils

import android.content.Context
import android.content.Intent
import com.thyview.extensions.launchModeWithNoBackStack
import com.thyview.storage.prefs.AccountPreferences
import com.thyview.ui.blanks.BlankNoNetworkConnectionActivity

class ScreenRouter {

    companion object {

        private const val lastSeenScreen = "lastSeenScreen"

        const val EMAIL_ACTIVITY = "EMAIL_ACTIVITY"

        fun setLastSeenScreen(screenTag: String, context: Context) {
            val sharedPreferences = AccountPreferences(context)
            sharedPreferences.setValue(lastSeenScreen, screenTag)
        }

        fun getLastScreenScreen(context: Context): String {
            val sharedPreferences = AccountPreferences(context)
            return sharedPreferences.getStringValue(lastSeenScreen, "")
        }

        fun navigate(screenTag: String, context: Context) {
            when (screenTag) {
//                EMAIL_ACTIVITY -> {
//                    navigateToScreen(context, EmailActivity::class.java)
//                }
            }
        }

        private fun navigateToScreen(context: Context, activityName: Class<*>) {
            val intent = Intent(context, activityName).launchModeWithNoBackStack()
            context.startActivity(intent)
        }

        fun navigateToBlankNoNetworkConnectionScreen(context: Context) {
            val intent = Intent(context, BlankNoNetworkConnectionActivity::class.java)
            context.startActivity(intent)
        }

//        fun saveScreenInfoToFirebase(context: Context, screenName: String) {
//            val map = hashMapOf<String, String>()
//            map[MP_SCREEN_NAME] = screenName
//            MixPanelAnalyticsTrackingService.logEvent(context, MP_SCREEN_VIEWED, map)
//        }
    }
}