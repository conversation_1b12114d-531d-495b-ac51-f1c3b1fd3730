package com.thyview.utils

import android.util.Log
import com.google.firebase.crashlytics.FirebaseCrashlytics
import com.thyview.BuildConfig
import timber.log.Timber

class TimberLogger {

    init {

        if (BuildConfig.DEBUG) {

            Timber.plant(Timber.DebugTree())
        } else {

            Timber.plant(CrashReportingTree())
        }
    }

    private inner class CrashReportingTree : Timber.Tree() {

        override fun log(priority: Int,
                         tag: String?,
                         message: String,
                         throwable: Throwable?) {

            if (priority == Log.ERROR || priority == Log.WARN) {

                if (throwable != null) {

                    FirebaseCrashlytics.getInstance().recordException(throwable)
                } else if (message.isNotEmpty()) {

                    val exception = LoveBeatException(errorCause = message)

                    // For crashlytics, Logs cannot be standalone. They need to combine with exception. For that reason
                    // we are doing both logging message and exception
                    FirebaseCrashlytics.getInstance().log(message)
                    FirebaseCrashlytics.getInstance().recordException(exception)
                }
            }
        }
    }
}