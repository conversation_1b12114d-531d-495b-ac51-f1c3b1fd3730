package com.thyview.utils

import android.content.Context
import android.view.View
import android.widget.ProgressBar
import androidx.appcompat.app.AlertDialog

class AppLogic(val context: Context) {

    companion object {

        fun deleteAccount(context: Context?, progressBar: ProgressBar) {
            val dialogBuilder = context?.let { AlertDialog.Builder(it) }

            dialogBuilder?.setMessage("This cannot be undone.")
                    ?.setCancelable(false)
                    ?.setPositiveButton("Delete") { dialog, id ->
                        progressBar.visibility = View.VISIBLE
                    }
                    ?.setNegativeButton("Cancel") { dialog, id ->
                        progressBar.visibility = View.GONE
                        dialog.cancel()
                    }

            val alert = dialogBuilder?.create()
            alert?.setTitle("Are you sure you want to delete your account?")
            alert?.show()
        }
    }

    private fun showAlert(title: String, message: String) {

        val alertDialog = AlertDialog.Builder(context).create()
        alertDialog.setTitle(title)
        alertDialog.setMessage(message)
        alertDialog.setButton(AlertDialog.BUTTON_POSITIVE, "Done"
        ) { dialog, which -> dialog.dismiss() }
        alertDialog.show()
    }
}