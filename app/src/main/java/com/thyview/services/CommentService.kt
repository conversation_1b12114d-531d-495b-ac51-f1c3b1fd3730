package com.thyview.services

import com.google.gson.Gson
import com.thyview.models.reviews.Comment
import com.thyview.services.api.CommentApiService
import timber.log.Timber
import javax.inject.Inject

/**
 * Service class for handling comment-related API operations using REST API
 */
class CommentService @Inject constructor(
    private val commentApiService: CommentApiService,
    private val gson: Gson
) {

    /**
     * Like or unlike a comment
     *
     * @param postId ID of the post containing the comment
     * @param commentId ID of the comment to like/unlike
     * @return LikeCommentResult indicating whether the comment is now liked and the updated like count
     */
    suspend fun toggleLike(postId: String, commentId: String): LikeCommentResult {
        try {
            val response = commentApiService.toggleLike(postId, commentId)

            val liked = response["liked"] as? Boolean ?: false
            val likeCount = (response["likeCount"] as? Number)?.toInt() ?: 0

            return LikeCommentResult(liked, likeCount)
        } catch (e: Exception) {
            Timber.e(e, "Error toggling like for comment $commentId")
            return LikeCommentResult(false, 0)
        }
    }

    /**
     * Data class for like comment response
     */
    data class LikeCommentResult(
        val liked: Boolean,
        val likeCount: Int
    )




    /**
     * Get replies for a comment
     *
     * @param postId ID of the post containing the comment
     * @param commentId ID of the comment to fetch replies for
     * @return List of reply comments
     */
    suspend fun getReplies(postId: String, commentId: String): List<Comment> {
        try {
            val response = commentApiService.getReplies(postId, commentId)

            // Parse the response
            val repliesArray = response["replies"] as? List<*>

            return repliesArray?.mapNotNull { replyData ->
                if (replyData is Map<*, *>) {
                    try {
                        @Suppress("UNCHECKED_CAST")
                        val commentMap = replyData as Map<String, Any>
                        val jsonString = gson.toJson(commentMap)
                        gson.fromJson(jsonString, Comment::class.java)
                    } catch (e: Exception) {
                        Timber.e(e, "Error parsing reply comment data")
                        null
                    }
                } else {
                    null
                }
            } ?: emptyList()
        } catch (e: Exception) {
            Timber.e(e, "Error getting replies for comment $commentId")
            return emptyList()
        }
    }
}
