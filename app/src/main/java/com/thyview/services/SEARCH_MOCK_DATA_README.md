# Search Feature Mock Data Documentation

## Overview
The search feature has been implemented with comprehensive mock data following the same pattern as the existing `AWSPostService.kt`. The mock data can be enabled/disabled using the `useMockData` flag in `SearchService.kt`.

## Mock Data Flag
```kotlin
val useMockData = true  // Set to false to use real API calls
```

## Available Mock Search Queries

### 1. **"avengers"** - Returns Marvel Avengers movies
- The Avengers (2012) - ID: 24428
- Avengers: Infinity War (2018) - ID: 299536

### 2. **"stranger things"** - Returns Netflix TV series
- Stranger Things (2016-2022) - ID: 66732

### 3. **"leonardo dicaprio"** - Returns person details
- <PERSON> - ID: 6193

### 4. **"action"** - Returns action genre movies
- Mad Max: Fury Road (2015) - ID: 28
- The Dark Knight (2008) - ID: 155

### 5. **"batman"** - Returns Batman movies
- The Dark Knight (2008) - ID: 155
- The Batman (2022) - ID: 414906

### 6. **"marvel"** - Returns Marvel movies
- The Avengers (2012) - ID: 24428
- <PERSON> Man (2008) - ID: 1726

## Mock Movie Details Available
- **24428**: The Avengers (2012) - Complete details with cast, crew, similar movies
- **299536**: Avengers: Infinity War (2018) - Complete details
- **155**: The Dark Knight (2008) - Complete details with Batman cast
- **1726**: Iron Man (2008) - Complete details with Marvel cast

## Mock TV Details Available
- **66732**: Stranger Things - Complete TV series details with cast

## Mock Person Details Available
- **6193**: Leonardo DiCaprio - Complete filmography and biography
- **3223**: Robert Downey Jr. - Complete Marvel filmography

## Mock Features

### Search Functionality
- **Debounced search**: 500ms delay after user stops typing
- **Minimum 3 characters**: Search only triggers with 3+ characters
- **Fuzzy matching**: Searches work with partial matches (e.g., "aven" matches "avengers")
- **Empty results**: Queries not in mock data return empty results gracefully

### Movie/TV/Person Details
- **Complete data**: All details screens have comprehensive mock data
- **Cast navigation**: Clicking cast members navigates to person details
- **Similar/recommended content**: Related movies and shows included

### Watchlist Functionality
- **Mock toggle**: Watchlist add/remove works with mock responses
- **Status check**: Items with ID divisible by 3 are "in watchlist" by default
- **Persistent state**: Watchlist state persists during app session

### Streaming Services
- **Mock services**: Returns Netflix, Prime Video, Disney+, HBO Max, Hulu
- **Universal availability**: All content shows same streaming services for demo

## Testing the Mock Data

### Search Testing
1. Open the app and navigate to Search tab
2. Type any of the mock queries above (minimum 3 characters)
3. Results should appear after 500ms delay
4. Tap any result to navigate to details screen

### Details Testing
1. From search results, tap any movie/TV/person
2. Verify all details load correctly
3. Test watchlist toggle functionality
4. Test "Where to watch" button
5. Test cast member navigation

### Navigation Testing
1. Test back navigation from all screens
2. Verify proper title bars and navigation icons
3. Test deep linking between screens

## Switching to Real API

To switch from mock data to real API calls:

1. Set `useMockData = false` in `SearchService.kt`
2. Ensure your API endpoints are properly configured
3. Update base URLs in `NetworkModule.kt` if needed
4. Test with real API responses

## Mock Data Structure

The mock data follows the exact same structure as the API responses defined in:
- `SearchModels.kt` - All data classes
- `SearchApiService.kt` - API interface definitions
- API response JSON files in `/apiResponses/` folder

## Error Handling

- **Network errors**: Mock data never fails, always returns success
- **Missing data**: Returns appropriate error messages for non-existent IDs
- **Empty results**: Returns empty arrays for unmatched search queries
- **Graceful fallbacks**: Real API calls fall back to mock data on errors

## Performance

- **Instant responses**: Mock data returns immediately
- **Memory efficient**: Data is lazily loaded and cached
- **Production ready**: Same patterns as existing services

## Future Enhancements

To add more mock data:
1. Add new entries to `mockSearchResults` map
2. Add corresponding details to `mockMovieDetails`, `mockTVDetails`, or `mockPersonDetails`
3. Follow the same data structure patterns
4. Test with the new search queries

The mock implementation provides a complete, production-ready search experience for development and testing purposes.
