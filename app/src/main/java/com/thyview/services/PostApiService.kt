package com.thyview.services

import retrofit2.http.*

/**
 * REST API interface for post-related operations
 */
interface PostApiService {
    
    /**
     * Fetches a page of posts
     */
    @GET("/posts")
    suspend fun getPosts(
        @Query("limit") limit: Int = 10,
        @Query("startAfter") startAfter: String? = null
    ): Map<String, Any>
    
    /**
     * Gets detailed information about a single post
     */
    @GET("/posts/{postId}")
    suspend fun getPostDetail(
        @Path("postId") postId: String
    ): Map<String, Any>
    
    /**
     * Toggles like/unlike on a post
     */
    @POST("/posts/{postId}/like")
    suspend fun likePost(
        @Path("postId") postId: String
    ): Map<String, Any>
    
    /**
     * Checks if the current user has liked a post
     */
    @GET("/posts/{postId}/liked")
    suspend fun hasUserLiked(
        @Path("postId") postId: String
    ): Map<String, Any>
    
    /**
     * Creates a new post with optional text and image
     */
    @POST("/posts")
    suspend fun createPost(
        @Body data: Map<String, Any?>
    ): Map<String, Any>
    
    /**
     * Edits an existing post
     */
    @PUT("/posts/{postId}")
    suspend fun editPost(
        @Path("postId") postId: String,
        @Body data: Map<String, Any?>
    ): Map<String, Any>
    
    /**
     * Deletes a post
     */
    @DELETE("/posts/{postId}")
    suspend fun deletePost(
        @Path("postId") postId: String
    ): Map<String, Any>
    
    /**
     * Reports a post for moderation
     */
    @POST("/posts/{postId}/report")
    suspend fun reportPost(
        @Path("postId") postId: String,
        @Body data: Map<String, Any>
    ): Map<String, Any>
    
    /**
     * Gets comments for a post
     */
    @GET("/posts/{postId}/comments")
    suspend fun getComments(
        @Path("postId") postId: String
    ): Map<String, Any>
    
    /**
     * Adds a comment or reply to a post
     */
    @POST("/posts/{postId}/comments")
    suspend fun submitComment(
        @Path("postId") postId: String,
        @Body data: Map<String, Any?>
    ): Map<String, Any>
    
    /**
     * Deletes a comment from a post
     */
    @DELETE("/posts/{postId}/comments/{commentId}")
    suspend fun deleteComment(
        @Path("postId") postId: String,
        @Path("commentId") commentId: String
    ): Map<String, Any>
    
    /**
     * Reports a comment for moderation
     */
    @POST("/posts/{postId}/comments/{commentId}/report")
    suspend fun reportComment(
        @Path("postId") postId: String,
        @Path("commentId") commentId: String,
        @Body data: Map<String, Any>
    ): Map<String, Any>
    
    /**
     * Gets replies to a specific comment
     */
    @GET("/posts/{postId}/comments/{commentId}/replies")
    suspend fun getReplies(
        @Path("postId") postId: String,
        @Path("commentId") commentId: String
    ): Map<String, Any>
    
    /**
     * Gets a signed URL for uploading an image
     */
    @POST("/images/upload-url")
    suspend fun getImageUploadUrl(
        @Body data: Map<String, Any>
    ): Map<String, Any>
}