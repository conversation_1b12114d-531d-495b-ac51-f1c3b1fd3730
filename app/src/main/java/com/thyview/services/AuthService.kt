package com.thyview.services

import android.content.Context
import com.google.firebase.auth.ktx.auth
import com.google.firebase.ktx.Firebase
import com.thyview.glide.MyAppGlideModule
import com.thyview.deepLinking.PromotionsHandler
import com.thyview.models.UserObject
import com.thyview.storage.prefs.AccountPreferences
import com.thyview.utils.Constants

class AuthService {

    companion object {

        fun cleanup(context: Context?) {

            val influencerId = AccountPreferences.getInstance(context).getStringValue(Constants.influencerId, "")
            val influencerName = AccountPreferences.getInstance(context).getStringValue(Constants.influencerName, "")
            val installSource = AccountPreferences.getInstance(context).getStringValue(Constants.installSource, "")

            AccountPreferences.getInstance(context).clear()
            UserObject.cleanup()

            if (!influencerId.isNullOrEmpty() && context != null) {
                PromotionsHandler.handleInfluencerSignups(context, influencerId, influencerName)
            }

            if (!installSource.isNullOrEmpty() && context != null) {
                //PromotionsHandler.handleBranchCampaignSignups(context, installSource)
            }

            context?.let {
                MyAppGlideModule.clearCache(it)
            }
        }

        fun isUserAuthenticated(): Boolean {
            return Firebase.auth.currentUser != null
        }
    }
}