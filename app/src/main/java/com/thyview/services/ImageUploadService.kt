package com.thyview.services

import android.content.Context
import android.net.Uri
import com.thyview.repository.PostRepository
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.flow
import kotlinx.coroutines.withContext
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.RequestBody.Companion.asRequestBody
import timber.log.Timber
import java.io.File
import java.io.IOException
import java.util.*
import java.util.concurrent.TimeUnit
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Service for handling image uploads
 */
@Singleton
class ImageUploadService @Inject constructor(
    private val postRepository: PostRepository,
    private val context: Context
) {
    private val okHttpClient = OkHttpClient.Builder()
        .connectTimeout(30, TimeUnit.SECONDS)
        .readTimeout(30, TimeUnit.SECONDS)
        .writeTimeout(30, TimeUnit.SECONDS)
        .build()

    /**
     * Upload progress state
     */
    sealed class UploadState {
        object Idle : UploadState()
        data class Preparing(val message: String = "Preparing upload...") : UploadState()
        data class Uploading(val progress: Int) : UploadState()
        data class Success(val imageUrl: String) : UploadState()
        data class Error(val message: String) : UploadState()
    }

    /**
     * Upload an image file and return a flow of upload states
     */
    fun uploadImage(fileUri: Uri): Flow<UploadState> = flow {
        emit(UploadState.Preparing())

        try {
            // Get content resolver to read the file
            val contentResolver = context.contentResolver
            val mimeType = contentResolver.getType(fileUri) ?: "image/jpeg"
            
            // Generate a unique filename
            val fileExtension = mimeType.split("/").lastOrNull() ?: "jpg"
            val fileName = "image_${UUID.randomUUID()}.$fileExtension"
            
            emit(UploadState.Preparing("Getting upload URL..."))
            
            // Get signed URL for upload
            val uploadUrls = postRepository.getImageUploadUrl(fileName, mimeType)
                ?: throw IOException("Failed to get upload URL")
            
            val (uploadUrl, publicUrl) = uploadUrls
            
            // Create a temporary file from the content URI
            val tempFile = withContext(Dispatchers.IO) {
                val inputStream = contentResolver.openInputStream(fileUri)
                    ?: throw IOException("Failed to open input stream")
                
                val tempFile = File.createTempFile("upload_", ".$fileExtension", context.cacheDir)
                tempFile.outputStream().use { outputStream ->
                    inputStream.copyTo(outputStream)
                }
                inputStream.close()
                tempFile
            }
            
            emit(UploadState.Uploading(0))
            
            // Upload the file
            val result = withContext(Dispatchers.IO) {
                val fileBody = tempFile.asRequestBody(mimeType.toMediaTypeOrNull())
                
                val request = Request.Builder()
                    .url(uploadUrl)
                    .put(fileBody)
                    .build()
                
                try {
                    emit(UploadState.Uploading(50))
                    val response = okHttpClient.newCall(request).execute()
                    
                    if (!response.isSuccessful) {
                        throw IOException("Upload failed with code ${response.code}")
                    }
                    
                    emit(UploadState.Uploading(100))
                    publicUrl
                } finally {
                    // Clean up the temporary file
                    tempFile.delete()
                }
            }
            
            emit(UploadState.Success(result))
        } catch (e: Exception) {
            Timber.e(e, "Image upload failed")
            emit(UploadState.Error(e.message ?: "Unknown error occurred"))
        }
    }
}