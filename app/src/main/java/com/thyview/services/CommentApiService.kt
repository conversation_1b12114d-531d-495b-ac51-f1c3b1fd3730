package com.thyview.services.api

import com.thyview.models.reviews.Comment
import retrofit2.http.*

/**
 * REST API interface for comment-related operations
 */
interface CommentApiService {
    
    /**
     * Like or unlike a comment
     */
    @POST("/posts/{postId}/comments/{commentId}/like")
    suspend fun toggleLike(
        @Path("postId") postId: String,
        @Path("commentId") commentId: String
    ): Map<String, Any>
    
    /**
     * Get replies for a comment
     */
    @GET("/posts/{postId}/comments/{commentId}/replies")
    suspend fun getReplies(
        @Path("postId") postId: String,
        @Path("commentId") commentId: String
    ): Map<String, Any>
}