package com.thyview.services.articles

import com.thyview.models.articles.ArticleLikeRequest
import com.thyview.models.articles.ArticleLikeResponse
import com.thyview.models.articles.ArticlesResponse
import retrofit2.http.Body
import retrofit2.http.GET
import retrofit2.http.POST
import retrofit2.http.Query

interface ArticlesApiService {
    
    @GET("/articles")
    suspend fun getArticles(
        @Query("limit") limit: Int? = 20,
        @Query("lastKey") lastKey: String? = null,
        @Query("postedBy") postedBy: String? = null,
        @Query("tags") tags: String? = null,
        @Query("sortBy") sortBy: String? = null,
        @Query("order") order: String? = null,
        @Query("userId") userId: String? = null
    ): ArticlesResponse
    
    @POST("/articles/like")
    suspend fun likeArticle(@Body request: ArticleLikeRequest): ArticleLikeResponse
    
    @POST("/articles/unlike")
    suspend fun unlikeArticle(@Body request: ArticleLikeRequest): ArticleLikeResponse
}
