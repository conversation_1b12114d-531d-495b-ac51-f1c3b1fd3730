package com.thyview.services

import com.thyview.models.reviews.Comment
import com.thyview.models.reviews.Post

/**
 * Interface for services that provide post and comment operations.
 */
interface IPostService {
    /**
     * Fetches a page of posts.
     * 
     * @param startAfter Optional parameter to fetch posts created after a specific timestamp
     * @return List of Post objects
     */
    suspend fun getPosts(startAfter: String? = null): List<Post>
    
    /**
     * Get detailed information about a specific post, including its comments.
     * Returns a pair of (post, comments) where post may be null if not found.
     */
    suspend fun getPostDetail(postId: String): Pair<Post?, List<Comment>>
    
    /**
     * Toggles like/unlike on a post.
     * 
     * @param postId The ID of the post to like/unlike
     * @return Boolean indicating if the post is now liked (true) or unliked (false)
     */
    suspend fun likePost(postId: String): Boolean
    
    /**
     * Check if the current user has liked a specific post.
     */
    suspend fun hasUserLiked(postId: String): Boolean
    
    /**
     * Create a new post with optional text and image URL.
     * Returns the ID of the created post, or null if creation failed.
     */
    suspend fun createPost(text: String?, imageUrl: String?): String?
    
    /**
     * Edit an existing post.
     * Returns true if the edit was successful.
     */
    suspend fun editPost(postId: String, text: String?, imageUrl: String?): Boolean
    
    /**
     * Delete a post.
     * Returns true if deletion was successful.
     */
    suspend fun deletePost(postId: String): Boolean
    
    /**
     * Report a post for moderation.
     * Returns true if the report was submitted successfully.
     */
    suspend fun reportPost(postId: String, reason: String): Boolean
    
    /**
     * Get the top-level comments for a post.
     */
    suspend fun getComments(postId: String): List<Comment>
    
    /**
     * Submit a new comment on a post or as a reply to another comment.
     * Returns the ID of the created comment, or null if creation failed.
     */
    suspend fun submitComment(
        postId: String,
        text: String,
        parentCommentId: String? = null,
        imageUrl: String? = null
    ): String?
    
    /**
     * Delete a comment.
     * Returns true if deletion was successful.
     */
    suspend fun deleteComment(postId: String, commentId: String): Boolean
    
    /**
     * Report a comment for moderation.
     * Returns true if the report was submitted successfully.
     */
    suspend fun reportComment(postId: String, commentId: String, reason: String): Boolean
    
    /**
     * Get the replies to a specific comment.
     */
    suspend fun getReplies(postId: String, commentId: String): List<Comment>
    
    /**
     * Get a signed URL for uploading an image directly to storage.
     * Returns a pair of (uploadUrl, publicUrl) or null if the operation failed.
     */
    suspend fun getImageUploadUrl(fileName: String, contentType: String): Pair<String, String>?
}
