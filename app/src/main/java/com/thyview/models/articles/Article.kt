package com.thyview.models.articles

import com.google.gson.annotations.SerializedName

data class Article(
    @SerializedName("id") val id: String,
    @SerializedName("title") val title: String,
    @SerializedName("description") val description: String,
    @SerializedName("imageUrl") val imageUrl: String?,
    @SerializedName("contentUrl") val contentUrl: String,
    @SerializedName("likes") val likes: Int,
    @SerializedName("createdAt") val createdAt: String,
    @SerializedName("modifiedAt") val modifiedAt: String?,
    @SerializedName("postedBy") val postedBy: String,
    @SerializedName("additionalNotes") val additionalNotes: String?,
    @SerializedName("content") val content: String?,
    @SerializedName("tags") val tags: List<String>?,
    @SerializedName("isLikedByUser") val isLikedByUser: Boolean = false
)

data class ArticlesResponse(
    @SerializedName("success") val success: Boolean,
    @SerializedName("data") val data: ArticlesData?
)

data class ArticlesData(
    @SerializedName("items") val items: List<Article>,
    @SerializedName("nextKey") val nextKey: String?
)

data class ArticleLikeRequest(
    @SerializedName("articleId") val articleId: String,
    @SerializedName("userId") val userId: String
)

data class ArticleLikeResponse(
    @SerializedName("success") val success: Boolean,
    @SerializedName("message") val message: String?
)

data class ErrorResponse(
    @SerializedName("success") val success: Boolean,
    @SerializedName("error") val error: String
)