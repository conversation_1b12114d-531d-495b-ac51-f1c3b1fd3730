package com.thyview.models.reviews

import java.time.Instant

object SampleData {
    val posts = listOf(
        Post(
            id = "1",
            externalAuthorId = "rrr_movie_fans",
            profileImageUrl = "https://placehold.co/100x100",
            title = "28 Years Later",
            rating = 4.0f,
            content = "The cinematography and emotion are top notch. <PERSON><PERSON><PERSON> did it again.",
            username = "<PERSON>",
            imageUrl = "https://placehold.co/600x300",
            likeCount = 214,
            commentCount = 42,
            createdAt = Instant.now().toString(),
            liked = false
        ),
        Post(
            id = "2",
            externalAuthorId = "kgf_diehards",
            profileImageUrl = "https://placehold.co/100x100",
            title = "KGF Chapter 2",
            rating = 4.5f,
            content = "Let's discuss <PERSON><PERSON>'s performance and the pacing between the two.",
            username = "<PERSON>",
            imageUrl = null,
            likeCount = 182,
            commentCount = 28,
            createdAt = Instant.now().minusSeconds(3600).toString(),
            liked = true
        )
    )

    val comments = listOf(
        Comment(
            id = "comment1",
            externalAuthorId = "user123",
            username = "<PERSON>",
            text = "I completely agree with your assessment!",
            createdAt = Instant.now().toString(),
            replyCount = 2
        ),
        Comment(
            id = "comment2",
            externalAuthorId = "moviefan456",
            username = "John",
            text = "The cinematography was breathtaking.",
            createdAt = Instant.now().minusSeconds(1800).toString(),
            replyCount = 0
        )
    )

    val replies = listOf(
        Comment(
            id = "reply1",
            externalAuthorId = "cinephile789",
            username = "John",
            text = "The action sequences were particularly well done.",
            parentCommentId = "comment1",
            createdAt = Instant.now().minusSeconds(900).toString(),
            replyCount = 0
        ),
        Comment(
            id = "reply2",
            externalAuthorId = "filmcritic101",
            text = "I think the character development could have been stronger.",
            username = "John",
            parentCommentId = "comment1",
            createdAt = Instant.now().minusSeconds(600).toString(),
            replyCount = 0
        )
    )
    
    // Method to get sample posts
    fun getSamplePosts(): List<Post> {
        return posts
    }
    
    // Method to get all sample comments (including replies)
    fun getSampleComments(): List<Comment> {
        return comments + replies
    }
}
