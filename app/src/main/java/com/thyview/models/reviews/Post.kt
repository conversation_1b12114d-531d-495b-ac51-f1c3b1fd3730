// com/thyview/models/Post.kt
package com.thyview.models.reviews

/**
 * Represents a post made by a user.
 *
 * @param id                Unique post ID.
 * @param externalAuthorId  The user’s internal ID.
 * @param username          The display username of the author.
 * @param profileImageUrl   URL of the author’s profile photo.
 * @param title             Title of the post/review.
 * @param rating            Star rating (0.0 to 5.0, supports half-stars).
 * @param content           Body text of the post.
 * @param imageUrl          Optional URL of an image attached to the post.
 * @param likeCount         Number of likes.
 * @param commentCount      Number of top-level comments.
 * @param createdAt         ISO-8601 timestamp when created.
 * @param liked             Whether the current user has liked this post.
 * @param viewCount         Number of views.
 */
data class Post(
    val id: String,
    val externalAuthorId: String,
    val username: String,
    val profileImageUrl: String?,
    val title: String,
    val rating: Float,
    val content: String,
    val imageUrl: String?,
    val likeCount: Int,
    val commentCount: Int,
    val createdAt: String,
    val liked: Boolean,
    val viewCount: Int = 0
)
