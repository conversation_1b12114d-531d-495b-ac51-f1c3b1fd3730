package com.thyview.models.reviews

/**
 * Request parameters for submitting a comment or reply to a post.
 *
 * @param postId The unique identifier of the post to comment on
 * @param text The text content of the comment
 * @param parentCommentId Optional ID of the parent comment if this is a reply
 * @param imageUrl Optional URL to an image to attach to the comment
 */
data class SubmitCommentRequest(
    val postId: String,
    val text: String,
    val parentCommentId: String? = null,
    val imageUrl: String? = null
)