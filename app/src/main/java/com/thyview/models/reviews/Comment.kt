// com/thyview/models/Comment.kt
package com.thyview.models.reviews

/**
 * Represents a comment or reply on a post.
 *
 * @param id               Unique comment ID.
 * @param externalAuthorId The commenter’s internal ID.
 * @param username         The display username of the commenter.
 * @param text             Body text of the comment.
 * @param createdAt        ISO-8601 timestamp when created.
 * @param parentCommentId  If non-null, this is a reply to that comment.
 * @param imageUrl         Optional URL of an image attached to the comment.
 * @param replyCount       Number of direct replies to this comment.
 */
data class Comment(
    val id: String,
    val externalAuthorId: String,
    val username: String,
    val text: String,
    val createdAt: String,
    val parentCommentId: String? = null,
    val imageUrl: String? = null,
    val replyCount: Int = 0,
    val likeCount: Int = 0,     // ← now included
    val liked: Boolean = false  // ← and liked state
)
