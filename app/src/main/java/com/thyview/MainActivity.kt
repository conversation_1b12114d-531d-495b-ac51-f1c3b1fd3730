package com.thyview

import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.BottomAppBar
import androidx.compose.material3.NavigationBarItem
import androidx.compose.material3.NavigationBarItemDefaults
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Home
import androidx.compose.material.icons.filled.Person
import androidx.compose.material.icons.filled.Search
import androidx.compose.material.icons.filled.List
import androidx.compose.material.icons.filled.Star
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.vector.ImageVector
import androidx.compose.ui.res.stringResource
import androidx.navigation.NavController
import androidx.navigation.compose.currentBackStackEntryAsState
import androidx.navigation.compose.rememberNavController
import com.thyview.ui.AppNavigation
import com.thyview.ui.NavRoutes
import com.thyview.ui.ThyViewTheme
import androidx.compose.ui.graphics.Color
import dagger.hilt.android.AndroidEntryPoint

// IMDB-style colors
val ImdbYellow = Color(0xFFF5C518) // IMDB's signature yellow
val ImdbBlack = Color(0xFF000000) // Black for background
val ImdbDarkGray = Color(0xFF333333) // Dark gray for unselected items

@AndroidEntryPoint
class MainActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContent {
            ThyViewTheme {
                MainScreen()
            }
        }
    }
}

@Composable
fun MainScreen() {
    val navController = rememberNavController()
    val navBackStackEntry by navController.currentBackStackEntryAsState()
    val currentRoute = navBackStackEntry?.destination?.route
    
    // Hide bottom bar on splash screen, post detail screen, and comment replies screen
    val showBottomBar = currentRoute != NavRoutes.SPLASH && 
                       !isPostDetailScreen(currentRoute) &&
                       !isCommentRepliesScreen(currentRoute)

    Scaffold(
        bottomBar = {
            if (showBottomBar) {
                BottomNavigationBar(navController)
            }
        }
    ) { innerPadding ->
        AppNavigation(
            navController = navController,
            modifier = Modifier.padding(innerPadding)
        )
    }
}

@Composable
fun BottomNavigationBar(navController: NavController) {
    val items = listOf(
        BottomNavItem(
            title = stringResource(R.string.title_home),
            icon = Icons.Default.Home,
            route = NavRoutes.HOME
        ),
        BottomNavItem(
            title = stringResource(R.string.title_articles),
            icon = Icons.Default.List,
            route = NavRoutes.ARTICLES
        ),
        BottomNavItem(
            title = stringResource(R.string.title_reviews),
            icon = Icons.Default.Star,
            route = NavRoutes.REVIEWS
        ),
        BottomNavItem(
            title = stringResource(R.string.title_search),
            icon = Icons.Default.Search,
            route = NavRoutes.SEARCH
        ),
        BottomNavItem(
            title = stringResource(R.string.title_profile),
            icon = Icons.Default.Person,
            route = NavRoutes.PROFILE
        )
    )

    val navBackStackEntry by navController.currentBackStackEntryAsState()
    val currentRoute = navBackStackEntry?.destination?.route

    // Modified logic to exclude PostDetailScreen
    val shouldShowBottomBar = items.any { it.route == currentRoute } ||
                             (isDetailScreen(currentRoute) && !isPostDetailScreen(currentRoute)) ||
                             currentRoute == NavRoutes.LOGIN

    if (shouldShowBottomBar) {
        // Using IMDB-style colors for bottom app bar
        BottomAppBar(
            containerColor = ImdbBlack // IMDB uses black for bottom navigation
        ) {
            items.forEach { item ->
                val selected = currentRoute == item.route ||
                             (isDetailScreen(currentRoute) && isRelatedDetailScreen(currentRoute, item.route)) ||
                             (currentRoute == NavRoutes.LOGIN && item.route == NavRoutes.PROFILE)

                NavigationBarItem(
                    icon = {
                        Icon(
                            imageVector = item.icon,
                            contentDescription = item.title,
                            tint = if (selected) ImdbYellow else Color.White.copy(alpha = 0.7f)
                        )
                    },
                    label = {
                        Text(
                            text = item.title,
                            color = if (selected) ImdbYellow else Color.White.copy(alpha = 0.7f)
                        )
                    },
                    selected = selected,
                    onClick = {
                        if (currentRoute != item.route) {
                            navController.navigate(item.route) {
                                // Pop up to the splash screen
                                popUpTo(NavRoutes.SPLASH) {
                                    saveState = true
                                }
                                // Avoid multiple copies of the same destination
                                launchSingleTop = true
                                // Restore state when reselecting a previously selected item
                                restoreState = true
                            }
                        }
                    },
                    colors = NavigationBarItemDefaults.colors(
                        selectedIconColor = ImdbYellow,
                        selectedTextColor = ImdbYellow,
                        indicatorColor = ImdbBlack, // Make indicator same as background for IMDB style
                        unselectedIconColor = Color.White.copy(alpha = 0.7f),
                        unselectedTextColor = Color.White.copy(alpha = 0.7f)
                    )
                )
            }
        }
    }
}

// Function to check if the current route is the post detail screen
private fun isPostDetailScreen(route: String?): Boolean {
    return route?.startsWith("post_detail/") == true
}

// Function to check if the current route is the comment replies screen
private fun isCommentRepliesScreen(route: String?): Boolean {
    return route?.startsWith("replies/") == true
}

// Function to check if the current route is a detail screen
private fun isDetailScreen(route: String?): Boolean {
    return route?.startsWith("article_detail/") == true ||
            route?.startsWith("actor/") == true ||
            route?.startsWith("movie/") == true ||
            route?.startsWith("movie_details/") == true ||
            route?.startsWith("tv_details/") == true ||
            route?.startsWith("person_details/") == true ||
            route?.startsWith("replies/") == true ||
            isPostDetailScreen(route)
}

// Function to determine if a detail screen is related to a main route (for highlighting the correct tab)
private fun isRelatedDetailScreen(detailRoute: String?, mainRoute: String): Boolean {
    return when {
        detailRoute?.startsWith("article_detail/") == true -> mainRoute == NavRoutes.ARTICLES
        detailRoute?.startsWith("post_detail/") == true -> mainRoute == NavRoutes.REVIEWS
        detailRoute?.startsWith("replies/") == true -> mainRoute == NavRoutes.REVIEWS
        detailRoute?.startsWith("actor/") == true -> mainRoute == NavRoutes.SEARCH
        detailRoute?.startsWith("movie/") == true -> mainRoute == NavRoutes.SEARCH
        detailRoute?.startsWith("movie_details/") == true -> mainRoute == NavRoutes.SEARCH
        detailRoute?.startsWith("tv_details/") == true -> mainRoute == NavRoutes.SEARCH
        detailRoute?.startsWith("person_details/") == true -> mainRoute == NavRoutes.SEARCH
        else -> false
    }
}

data class BottomNavItem(
    val title: String,
    val icon: ImageVector,
    val route: String
)
