package com.thyview.repository

import com.thyview.di.PostServiceProvider
import com.thyview.models.reviews.Comment
import com.thyview.models.reviews.Post
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Repository that handles all post-related data operations.
 * Acts as the single source of truth for post data.
 */
@Singleton
class PostRepository @Inject constructor(
    private val serviceProvider: PostServiceProvider
) {
    // Get the appropriate service based on configuration
    private val service get() = serviceProvider.getPostService()

    /**
     * Fetch a list of posts, optionally starting after a specific creation time.
     */
    suspend fun getPosts(startAfter: String? = null): List<Post> = withContext(Dispatchers.IO) {
        service.getPosts(startAfter)
    }

    /**
     * Get detailed information about a specific post, including its comments.
     */
    suspend fun getPostDetail(postId: String): Pair<Post?, List<Comment>> = withContext(Dispatchers.IO) {
        service.getPostDetail(postId)
    }

    /**
     * Toggle the like status of a post.
     * Returns true if the post is now liked, false if it's now unliked.
     */
    suspend fun likePost(postId: String): Boolean = withContext(Dispatchers.IO) {
        service.likePost(postId)
    }

    /**
     * Check if the current user has liked a specific post.
     */
    suspend fun hasUserLiked(postId: String): Boolean = withContext(Dispatchers.IO) {
        service.hasUserLiked(postId)
    }

    /**
     * Create a new post with optional text and image URL.
     * Returns the ID of the created post, or null if creation failed.
     */
    suspend fun createPost(text: String?, imageUrl: String?): String? = withContext(Dispatchers.IO) {
        service.createPost(text, imageUrl)
    }

    /**
     * Edit an existing post.
     * Returns true if the edit was successful.
     */
    suspend fun editPost(postId: String, text: String?, imageUrl: String?): Boolean = withContext(Dispatchers.IO) {
        service.editPost(postId, text, imageUrl)
    }

    /**
     * Delete a post.
     * Returns true if deletion was successful.
     */
    suspend fun deletePost(postId: String): Boolean = withContext(Dispatchers.IO) {
        service.deletePost(postId)
    }

    /**
     * Report a post for moderation.
     * Returns true if the report was submitted successfully.
     */
    suspend fun reportPost(postId: String, reason: String): Boolean = withContext(Dispatchers.IO) {
        service.reportPost(postId, reason)
    }

    /**
     * Get the top-level comments for a post.
     */
    suspend fun getComments(postId: String): List<Comment> = withContext(Dispatchers.IO) {
        service.getComments(postId)
    }

    /**
     * Submit a new comment on a post or as a reply to another comment.
     * Returns the ID of the created comment, or null if creation failed.
     */
    suspend fun submitComment(
        postId: String,
        text: String,
        parentCommentId: String? = null,
        imageUrl: String? = null
    ): String? = withContext(Dispatchers.IO) {
        service.submitComment(postId, text, parentCommentId, imageUrl)
    }

    /**
     * Delete a comment.
     * Returns true if deletion was successful.
     */
    suspend fun deleteComment(postId: String, commentId: String): Boolean = withContext(Dispatchers.IO) {
        service.deleteComment(postId, commentId)
    }

    /**
     * Report a comment for moderation.
     * Returns true if the report was submitted successfully.
     */
    suspend fun reportComment(postId: String, commentId: String, reason: String): Boolean = 
        withContext(Dispatchers.IO) {
            service.reportComment(postId, commentId, reason)
        }

    /**
     * Get the replies to a specific comment.
     */
    suspend fun getReplies(postId: String, commentId: String): List<Comment> = withContext(Dispatchers.IO) {
        service.getReplies(postId, commentId)
    }

    /**
     * Get a signed URL for uploading an image directly to storage.
     * Returns a pair of (uploadUrl, publicUrl) or null if the operation failed.
     */
    suspend fun getImageUploadUrl(fileName: String, contentType: String): Pair<String, String>? = 
        withContext(Dispatchers.IO) {
            service.getImageUploadUrl(fileName, contentType)
        }
}
