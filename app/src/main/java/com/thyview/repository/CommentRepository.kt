package com.thyview.repository

import com.thyview.models.reviews.Comment
import com.thyview.models.reviews.Post
import com.thyview.services.AWSPostService
import com.thyview.services.CommentService
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

@Singleton
class CommentRepository @Inject constructor(
    private val postService: AWSPostService,
    private val commentService: CommentService
) {
    /**
     * Get a specific post by ID.
     */
    suspend fun getPost(postId: String): Post? {
        Timber.d("Getting post details for post $postId")
        return try {
            val (post, _) = postService.getPostDetail(postId)
            post
        } catch (e: Exception) {
            Timber.e(e, "Error getting post details")
            null
        }
    }
    
    /**
     * Get all comments for a post.
     */
    suspend fun getComments(postId: String): List<Comment> {
        Timber.d("Getting comments for post $postId")
        return try {
            val (_, comments) = postService.getPostDetail(postId)
            comments
        } catch (e: Exception) {
            Timber.e(e, "Error getting comments")
            emptyList()
        }
    }
    
    /**
     * Get replies for a specific comment.
     * This is an alias for getReplies to maintain consistent naming with the ViewModel.
     */
    suspend fun getCommentReplies(postId: String, commentId: String): List<Comment> {
        Timber.d("Getting replies for comment $commentId")
        return getReplies(postId, commentId)
    }
    
    /**
     * Get replies for a specific comment.
     */
    suspend fun getReplies(postId: String, commentId: String): List<Comment> {
        Timber.d("Getting replies for comment $commentId")
        return try {
            postService.getReplies(postId, commentId)
        } catch (e: Exception) {
            Timber.e(e, "Error getting replies")
            emptyList()
        }
    }
    
    /**
     * Submit a new comment to a post.
     */
    suspend fun submitComment(postId: String, text: String, imageUrl: String? = null): Boolean {
        Timber.d("Submitting comment to post $postId")
        return try {
            val commentId = postService.submitComment(postId, text, null, imageUrl)
            commentId != null
        } catch (e: Exception) {
            Timber.e(e, "Error submitting comment")
            false
        }
    }
    
    /**
     * Submit a reply to a specific comment.
     */
    suspend fun submitReply(postId: String, parentCommentId: String, text: String, imageUrl: String? = null): Boolean {
        Timber.d("Submitting reply to comment $parentCommentId")
        return try {
            val commentId = postService.submitComment(postId, text, parentCommentId, imageUrl)
            commentId != null
        } catch (e: Exception) {
            Timber.e(e, "Error submitting reply")
            false
        }
    }
    
    /**
     * Delete a comment.
     */
    suspend fun deleteComment(postId: String, commentId: String): Boolean {
        Timber.d("Deleting comment $commentId")
        return try {
            postService.deleteComment(postId, commentId)
        } catch (e: Exception) {
            Timber.e(e, "Error deleting comment")
            false
        }
    }
    
    /**
     * Like or unlike a comment.
     *
     * @param postId The ID of the post containing the comment
     * @param commentId The ID of the comment to like/unlike
     * @return LikeCommentResult containing liked status and like count
     */
    suspend fun toggleLike(postId: String, commentId: String): CommentService.LikeCommentResult {
        Timber.d("Toggling like for comment $commentId in post $postId")
        return try {
            commentService.toggleLike(postId, commentId)
        } catch (e: Exception) {
            Timber.e(e, "Error toggling like for comment")
            CommentService.LikeCommentResult(false, 0)
        }
    }


    /**
     * Get a specific comment by its ID
     * 
     * @param postId The ID of the post containing the comment
     * @param commentId The ID of the comment to retrieve
     * @return The Comment object if found, null otherwise
     */
    suspend fun getCommentById(postId: String, commentId: String): Comment? {
        Timber.d("Getting comment $commentId from post $postId")
        
        // First try to find it in the top-level comments
        val topLevelComments = getComments(postId)
        val foundInTopLevel = topLevelComments.find { it.id == commentId }
        if (foundInTopLevel != null) {
            return foundInTopLevel
        }
        
        // If not found, check each top-level comment's replies
        for (comment in topLevelComments) {
            if (comment.replyCount > 0) {
                // If this comment has replies, check them
                val replies = getReplies(postId, comment.id)
                val foundInReplies = replies.find { it.id == commentId }
                if (foundInReplies != null) {
                    return foundInReplies
                }
            }
        }
        
        // If we get here, the comment wasn't found
        Timber.d("Comment $commentId not found in post $postId")
        return null
    }
}
