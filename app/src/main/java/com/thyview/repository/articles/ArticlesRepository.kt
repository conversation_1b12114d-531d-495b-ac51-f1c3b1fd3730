package com.thyview.repository.articles

import android.util.Log
import com.thyview.models.articles.ArticleLikeRequest
import com.thyview.models.articles.ArticlesResponse

import com.thyview.services.articles.ArticlesApiService
import javax.inject.Inject
import javax.inject.Singleton
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

@Singleton
class ArticlesRepository @Inject constructor(
    private val articlesApiService: ArticlesApiService
) {
    private val TAG = "ArticlesRepository"
    
    suspend fun getArticles(
        limit: Int = 20,
        lastKey: String? = null,
        postedBy: String? = null,
        tags: String? = null,
        sortBy: String? = null,
        order: String? = null,
        userId: String? = null
    ): ArticlesResponse {
        return withContext(Dispatchers.IO) {
            try {
                val response = articlesApiService.getArticles(
                    limit = limit,
                    lastKey = lastKey,
                    postedBy = postedBy,
                    tags = tags,
                    sortBy = sortBy,
                    order = order,
                    userId = userId
                )
                Log.d(TAG, "Successfully fetched ${response.data?.items?.size ?: 0} articles")
                response
            } catch (error: Exception) {
                Log.e(TAG, "Error fetching articles: ${error.message}")
                throw error
            }
        }
    }
    
    suspend fun likeArticle(articleId: String, userId: String): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                val response = articlesApiService.likeArticle(
                    ArticleLikeRequest(articleId = articleId, userId = userId)
                )
                Log.d(TAG, "Like article result: ${response.success} for article $articleId")
                response.success
            } catch (error: Exception) {
                Log.e(TAG, "Error liking article $articleId: ${error.message}")
                throw error
            }
        }
    }
    
    suspend fun unlikeArticle(articleId: String, userId: String): Boolean {
        return withContext(Dispatchers.IO) {
            try {
                val response = articlesApiService.unlikeArticle(
                    ArticleLikeRequest(articleId = articleId, userId = userId)
                )
                Log.d(TAG, "Unlike article result: ${response.success} for article $articleId")
                response.success
            } catch (error: Exception) {
                Log.e(TAG, "Error unliking article $articleId: ${error.message}")
                throw error
            }
        }
    }
}
