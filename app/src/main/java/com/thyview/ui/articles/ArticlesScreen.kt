package com.thyview.ui.articles

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.PaddingValues
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.lazy.rememberLazyListState
import androidx.compose.material.ExperimentalMaterialApi
import androidx.compose.material.pullrefresh.PullRefreshIndicator
import androidx.compose.material.pullrefresh.pullRefresh
import androidx.compose.material.pullrefresh.rememberPullRefreshState
import androidx.compose.material3.Button
import androidx.compose.material3.ButtonDefaults
import androidx.compose.material3.CircularProgressIndicator
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.SnackbarHost
import androidx.compose.material3.SnackbarHostState
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.derivedStateOf
import androidx.compose.runtime.getValue
import androidx.compose.runtime.remember
import androidx.compose.runtime.rememberCoroutineScope
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavController
import androidx.navigation.compose.rememberNavController
import com.thyview.R
import com.thyview.models.articles.Article
import com.thyview.ui.NavRoutes
import com.thyview.ui.ThyViewTheme
import com.thyview.ui.ImdbBlack
import com.thyview.ui.ImdbDarkGray
import com.thyview.ui.ImdbYellow
import com.thyview.ui.ImdbWhite
import kotlinx.coroutines.launch

@OptIn(ExperimentalMaterial3Api::class, ExperimentalMaterialApi::class)
@Composable
fun ArticlesScreen(
    navController: NavController,
    viewModel: ArticlesViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState
    val listState = rememberLazyListState()
    val snackbarHostState = remember { SnackbarHostState() }
    val scope = rememberCoroutineScope()
    
    // Pull to refresh state
    val pullRefreshState = rememberPullRefreshState(
        refreshing = uiState.isLoading && uiState.articles.isEmpty(),
        onRefresh = { viewModel.refreshArticles() }
    )
    
    // Detect when we're near the end of the list to load more articles
    val shouldLoadMore by remember {
        derivedStateOf {
            val visibleItemCount = listState.layoutInfo.visibleItemsInfo.size
            val totalItemCount = listState.layoutInfo.totalItemsCount
            val firstVisibleItemIndex = listState.firstVisibleItemIndex
            
            // Load more when user scrolls to the last 3 items
            !uiState.isLoading && !uiState.isLastPage &&
                    totalItemCount > 0 &&
                    firstVisibleItemIndex + visibleItemCount >= totalItemCount - 3
        }
    }
    
    // Load more articles when approaching the end of the list
    LaunchedEffect(shouldLoadMore) {
        if (shouldLoadMore) {
            viewModel.loadMoreArticles()
        }
    }
    
    // Show error message as snackbar if there's an error
    LaunchedEffect(uiState.error) {
        uiState.error?.let { errorMessage ->
            scope.launch {
                snackbarHostState.showSnackbar(errorMessage)
            }
        }
    }
    
    ThyViewTheme {
        Scaffold(
            topBar = {
                TopAppBar(
                    title = { 
                        Text(
                            text = stringResource(R.string.title_articles),
                            color = ImdbYellow
                        ) 
                    },
                    colors = TopAppBarDefaults.topAppBarColors(
                        containerColor = ImdbBlack,
                        titleContentColor = ImdbYellow
                    )
                )
            },
            snackbarHost = { SnackbarHost(snackbarHostState) },
            containerColor = ImdbBlack // Set container color to IMDB black
        ) { innerPadding ->
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(innerPadding)
                    .background(ImdbBlack) // Set background to IMDB black
                    .pullRefresh(pullRefreshState)
        ) {
                if (uiState.isLoading && uiState.articles.isEmpty()) {
                    // Initial loading
                    CircularProgressIndicator(
                        modifier = Modifier.align(Alignment.Center),
                        color = ImdbYellow // Use IMDB yellow for loading indicator
                    )
                } else if (uiState.articles.isEmpty() && uiState.error == null) {
                    // Empty state
                    Column(
                        modifier = Modifier
                            .fillMaxSize()
                            .padding(16.dp),
                        horizontalAlignment = Alignment.CenterHorizontally,
                        verticalArrangement = Arrangement.Center
                    ) {
                        Text(
                            text = stringResource(R.string.no_articles_found),
                            style = MaterialTheme.typography.bodyLarge,
                            textAlign = TextAlign.Center,
                            color = ImdbWhite,
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(16.dp)
                        )
                        
                        Button(
                            onClick = { viewModel.refreshArticles() },
                            colors = ButtonDefaults.buttonColors(
                                containerColor = ImdbYellow,
                                contentColor = MaterialTheme.colorScheme.onPrimary
                            )
                        ) {
                            Text("Refresh")
                        }
                    }
                } else {
                    // Articles list
                    LazyColumn(
                        state = listState,
                        contentPadding = PaddingValues(vertical = 8.dp, horizontal = 16.dp),
                        verticalArrangement = Arrangement.spacedBy(16.dp),
                        modifier = Modifier
                            .fillMaxSize()
                            .background(ImdbBlack)
                ) {
                        items(uiState.articles) { article ->
                            ArticleItem(
                                article = article,
                                onArticleClick = { clickedArticle ->
                                    navigateToArticleDetail(navController, clickedArticle)
                                },
                                onLikeClick = { articleToLike ->
                                    viewModel.toggleLike(articleToLike)
                                },
                                // Add specific modifier to ensure consistent card styling
                                modifier = Modifier.fillMaxWidth()
                            )
                    }
                    
                        // Footer item for pagination loading indicator
                        if (uiState.isLoading && uiState.articles.isNotEmpty()) {
                            item {
                                Box(
                                    modifier = Modifier
                                        .fillMaxWidth()
                                        .padding(16.dp),
                                    contentAlignment = Alignment.Center
                                ) {
                                    CircularProgressIndicator(color = ImdbYellow)
                                }
                            }
                        }
                }
            }
                
                // Pull to refresh indicator
                PullRefreshIndicator(
                    refreshing = uiState.isLoading && uiState.articles.isEmpty(),
                    state = pullRefreshState,
                    modifier = Modifier.align(Alignment.TopCenter),
                    backgroundColor = ImdbDarkGray,
                    contentColor = ImdbYellow
                )
            }
        }
    }
}

// Helper function to navigate to the article detail screen
private fun navigateToArticleDetail(navController: NavController, article: Article) {
    navController.navigate(
        NavRoutes.articleDetail(
            articleId = article.id,
            title = article.title,
            contentUrl = article.contentUrl
        )
    )
}

@Preview(showBackground = true, widthDp = 360)
@Composable
fun ArticlesScreenPreview() {
    // Using a mock NavController for the preview
    val mockNavController = rememberNavController()
    
    // Displaying the ArticlesScreen with the mock NavController
    ThyViewTheme {
        ArticlesScreen(navController = mockNavController)
    }
}
