package com.thyview.ui.articles

import android.util.Log
import androidx.compose.runtime.State
import androidx.compose.runtime.mutableStateOf
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.thyview.repository.articles.ArticlesRepository
import com.thyview.models.articles.Article
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import javax.inject.Inject

@HiltViewModel
class ArticlesViewModel @Inject constructor(
    private val articlesRepository: ArticlesRepository
) : ViewModel() {
    private val TAG = "ArticlesViewModel"
    
    private val _uiState = mutableStateOf(ArticlesUiState())
    val uiState: State<ArticlesUiState> = _uiState
    
    // Temporary user ID - in a real app this would come from user session/authentication
    private val userId = "current-user-id"
    
    init {
        loadArticles()
    }
    
    fun loadArticles(forceRefresh: Boolean = false) {
        if (_uiState.value.isLoading) return
        
        if (forceRefresh) {
            _uiState.value = _uiState.value.copy(
                articles = emptyList(),
                lastKey = null,
                isLastPage = false,
                error = null
            )
        }
        
        // Don't load if we already know we're at the last page
        if (!forceRefresh && _uiState.value.isLastPage) return
        
        _uiState.value = _uiState.value.copy(isLoading = true)
        
        viewModelScope.launch {
            try {
                val response = articlesRepository.getArticles(
                    lastKey = if (forceRefresh) null else _uiState.value.lastKey,
                    userId = userId
                )
                
                if (response.success && response.data != null) {
                    val currentList = if (forceRefresh) emptyList() else _uiState.value.articles
                    val newArticles = response.data.items
                    
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        articles = currentList + newArticles,
                        lastKey = response.data.nextKey,
                        isLastPage = response.data.nextKey == null,
                        error = null
                    )
                    
                    Log.d(TAG, "Loaded ${newArticles.size} articles, next key: ${response.data.nextKey}")
                } else {
                    _uiState.value = _uiState.value.copy(
                        isLoading = false,
                        error = "Failed to load articles"
                    )
                    Log.e(TAG, "Error in response: ${response.success}")
                }
            } catch (error: Exception) {
                _uiState.value = _uiState.value.copy(
                    isLoading = false,
                    error = error.message ?: "Unknown error occurred"
                )
                Log.e(TAG, "Error loading articles", error)
            }
        }
    }
    
    fun toggleLike(article: Article) {
        val articles = _uiState.value.articles.toMutableList()
        val index = articles.indexOfFirst { it.id == article.id }
        
        if (index == -1) return
        
        val currentArticle = articles[index]
        val isCurrentlyLiked = currentArticle.isLikedByUser
        
        // Optimistically update UI
        val updatedArticle = currentArticle.copy(
            isLikedByUser = !isCurrentlyLiked,
            likes = if (isCurrentlyLiked) currentArticle.likes - 1 else currentArticle.likes + 1
        )
        articles[index] = updatedArticle
        _uiState.value = _uiState.value.copy(articles = articles)
        
        viewModelScope.launch {
            try {
                val success = if (isCurrentlyLiked) {
                    articlesRepository.unlikeArticle(currentArticle.id, userId)
                } else {
                    articlesRepository.likeArticle(currentArticle.id, userId)
                }
                
                if (!success) {
                    // Revert changes if API call failed
                    Log.e(TAG, "Failed to ${if (isCurrentlyLiked) "unlike" else "like"} article ${currentArticle.id}")
                    
                    val revertedArticles = _uiState.value.articles.toMutableList()
                    val currentIndex = revertedArticles.indexOfFirst { it.id == article.id }
                    
                    if (currentIndex != -1) {
                        revertedArticles[currentIndex] = currentArticle
                        _uiState.value = _uiState.value.copy(articles = revertedArticles)
                    }
                }
            } catch (error: Exception) {
                // Revert changes if API call failed
                Log.e(TAG, "Error ${if (isCurrentlyLiked) "unliking" else "liking"} article: ${error.message}", error)
                
                val revertedArticles = _uiState.value.articles.toMutableList()
                val currentIndex = revertedArticles.indexOfFirst { it.id == article.id }
                
                if (currentIndex != -1) {
                    revertedArticles[currentIndex] = currentArticle
                    _uiState.value = _uiState.value.copy(articles = revertedArticles)
                }
            }
        }
    }
    
    fun refreshArticles() {
        loadArticles(forceRefresh = true)
    }
    
    fun loadMoreArticles() {
        if (!_uiState.value.isLoading && !_uiState.value.isLastPage) {
            loadArticles()
        }
    }
}

data class ArticlesUiState(
    val isLoading: Boolean = false,
    val articles: List<Article> = emptyList(),
    val lastKey: String? = null,
    val isLastPage: Boolean = false,
    val error: String? = null
)
