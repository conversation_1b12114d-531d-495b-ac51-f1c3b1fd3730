package com.thyview.ui.reviews

import com.thyview.models.reviews.Comment
import java.time.Instant
import java.time.LocalDateTime
import java.time.ZoneId
import java.time.temporal.ChronoUnit

/**
 * Extension properties for Comment to provide UI-friendly date formatting
 */

/**
 * Returns a human-readable date string based on the comment's createdAt timestamp
 */
val Comment.date: String
    get() {
        if (createdAt.isBlank()) return ""
        
        try {
            val commentTime = Instant.parse(createdAt)
            val now = Instant.now()
            
            val diffMinutes = ChronoUnit.MINUTES.between(commentTime, now)
            val diffHours = ChronoUnit.HOURS.between(commentTime, now)
            val diffDays = ChronoUnit.DAYS.between(commentTime, now)
            
            return when {
                diffMinutes < 1 -> "Just now"
                diffMinutes == 1L -> "1 minute ago"
                diffMinutes < 60 -> "$diffMinutes minutes ago"
                diffHours == 1L -> "1 hour ago"
                diffHours < 24 -> "$diffHours hours ago"
                diffDays == 1L -> "Yesterday"
                diffDays < 7 -> "$diffDays days ago"
                diffDays < 30 -> "${diffDays / 7} weeks ago"
                else -> {
                    // For older dates, return a formatted date
                    val dateTime = LocalDateTime.ofInstant(commentTime, ZoneId.systemDefault())
                    val month = dateTime.month.toString().toLowerCase().capitalize()
                    val day = dateTime.dayOfMonth
                    val year = if (dateTime.year != LocalDateTime.now().year) ", ${dateTime.year}" else ""
                    "$month $day$year"
                }
            }
        } catch (e: Exception) {
            // Fallback in case of parsing errors
            return "Unknown date"
        }
    }

private fun String.capitalize(): String {
    return if (this.isNotEmpty()) this.substring(0, 1).uppercase() + this.substring(1) else this
}