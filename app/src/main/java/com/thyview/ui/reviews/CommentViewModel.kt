package com.thyview.ui.reviews

import androidx.lifecycle.SavedStateHandle
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.thyview.models.reviews.Comment
import com.thyview.models.reviews.Post
import com.thyview.repository.CommentRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

/**
 * ViewModel for managing comment operations including viewing, submitting, and liking.
 */
@HiltViewModel
class CommentViewModel @Inject constructor(
    private val repository: CommentRepository,
    savedStateHandle: SavedStateHandle
) : ViewModel() {
    
    private val postId = savedStateHandle.get<String>("postId") ?: ""
    
    private val _uiState = MutableStateFlow(CommentUiState())
    val uiState: StateFlow<CommentUiState> = _uiState.asStateFlow()
    
    // Track if we're showing replies to a specific comment
    private var parentCommentId: String? = null
    
    init {
        if (postId.isNotEmpty()) {
            loadPostAndComments()
        }
    }
    
    /**
     * Set the parent comment ID to load replies for a specific comment
     */
    fun setParentCommentId(commentId: String?) {
        parentCommentId = commentId
        loadComments()
    }
    
    fun loadPostAndComments() {
        loadPost()
        loadComments()
    }
    
    private fun loadPost() {
        viewModelScope.launch {
            try {
                val post = repository.getPost(postId)
                _uiState.update { it.copy(post = post) }
            } catch (e: Exception) {
                Timber.e(e, "Error loading post")
                // Handle error
            }
        }
    }
    
    fun loadComments() {
        viewModelScope.launch {
            _uiState.update { it.copy(commentListState = OperationState.Loading) }
            try {
                val comments = if (parentCommentId != null) {
                    // If we have a parent comment ID, load replies to that comment
                    repository.getCommentReplies(postId, parentCommentId!!)
                } else {
                    // Otherwise load top-level comments
                    repository.getComments(postId)
                }
                _uiState.update { 
                    it.copy(
                        comments = comments,
                        commentListState = OperationState.Success
                    ) 
                }
            } catch (e: Exception) {
                Timber.e(e, "Error loading comments")
                _uiState.update { 
                    it.copy(
                        commentListState = OperationState.Error(e.message ?: "Failed to load comments")
                    ) 
                }
            }
        }
    }
    
    fun onCommentTextChange(text: String) {
        _uiState.update { it.copy(commentText = text) }
    }
    
    fun submitComment() {
        val commentText = _uiState.value.commentText.trim()
        if (commentText.isNotEmpty()) {
            viewModelScope.launch {
                _uiState.update { it.copy(submitState = OperationState.Loading) }
                try {
                    val success = if (parentCommentId != null) {
                        // Submit as a reply to the parent comment
                        repository.submitReply(postId, parentCommentId!!, commentText)
                    } else {
                        // Submit as a top-level comment
                        repository.submitComment(postId, commentText)
                    }
                    
                    if (success) {
                        _uiState.update { 
                            it.copy(
                                commentText = "",
                                submitState = OperationState.Success
                            ) 
                        }
                        loadComments() // Refresh comments after submitting
                    } else {
                        _uiState.update { 
                            it.copy(
                                submitState = OperationState.Error("Failed to submit comment")
                            ) 
                        }
                    }
                } catch (e: Exception) {
                    Timber.e(e, "Error submitting comment")
                    _uiState.update { 
                        it.copy(
                            submitState = OperationState.Error(e.message ?: "Failed to submit comment")
                        ) 
                    }
                }
            }
        }
    }
    
    fun onReply(commentId: String) { 
        // This could be implemented to show a reply UI or navigate to a reply screen
        // For now we just log the action
        Timber.d("Reply to comment $commentId requested")
    }
    
    /**
     * Like or unlike a post
     */
    fun onLike(postId: String) { 
        // This would handle post liking, not comment liking
        Timber.d("Like post $postId requested")
    }
    
    /**
     * Like or unlike a comment
     * @param commentId ID of the comment to like/unlike
     */
    fun likeComment(commentId: String) {
        viewModelScope.launch {
            _uiState.update { 
                it.copy(
                    commentOperations = it.commentOperations + (commentId to OperationState.Loading)
                ) 
            }
            
            try {
                val currentComment = uiState.value.comments.find { it.id == commentId }
                if (currentComment == null) {
                    _uiState.update { 
                        it.copy(
                            commentOperations = it.commentOperations + 
                                (commentId to OperationState.Error("Comment not found"))
                        ) 
                    }
                    return@launch
                }
                
                // Call repository to toggle like status
                val result = repository.toggleLike(postId, commentId)
                
                // Update the comment in the list with the new like state and count from the result
                val updatedComments = uiState.value.comments.map { comment ->
                    if (comment.id == commentId) {
                        comment.copy(liked = result.liked, likeCount = result.likeCount)
                    } else {
                        comment
                    }
                }
                
                _uiState.update { 
                    it.copy(
                        comments = updatedComments,
                        commentOperations = it.commentOperations + (commentId to OperationState.Success)
                    ) 
                }
            } catch (e: Exception) {
                Timber.e(e, "Error liking/unliking comment $commentId")
                _uiState.update { 
                    it.copy(
                        commentOperations = it.commentOperations + 
                            (commentId to OperationState.Error(e.message ?: "Error updating like"))
                    ) 
                }
            }
        }
    }
    
    /**
     * Clear the operation state for a specific comment
     * @param commentId ID of the comment to clear state for
     */
    fun clearCommentOperationState(commentId: String) {
        _uiState.update { 
            val updatedOperations = it.commentOperations.toMutableMap()
            updatedOperations.remove(commentId)
            it.copy(commentOperations = updatedOperations)
        }
    }
    
    /**
     * Get a specific comment by its ID
     * @param commentId The ID of the comment to retrieve
     * @return The Comment object if found, null otherwise
     */
    suspend fun getCommentById(commentId: String): Comment? {
        return try {
            // First try to get it from the current comments list
            uiState.value.comments.find { it.id == commentId }
                ?: repository.getCommentById(postId, commentId) // Otherwise fetch from repository
        } catch (e: Exception) {
            Timber.e(e, "Error getting comment by ID")
            null
        }
    }
}

/**
 * Represents the UI state for comment operations
 */
data class CommentUiState(
    val post: Post? = null,
    val comments: List<Comment> = emptyList(),
    val commentText: String = "",
    val commentListState: OperationState = OperationState.Initial,
    val submitState: OperationState = OperationState.Initial,
    // Map of commentId to its operation state (for like, delete operations)
    val commentOperations: Map<String, OperationState> = emptyMap()
)

/**
 * Represents the state of an operation
 */
sealed class OperationState {
    object Initial : OperationState()
    object Loading : OperationState()
    object Success : OperationState()
    data class Error(val message: String) : OperationState()
}
