
package com.thyview.ui.reviews

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.ui.res.painterResource
import com.thyview.R
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Comment
import androidx.compose.material.icons.filled.Favorite
import androidx.compose.material.icons.filled.FavoriteBorder
import androidx.compose.material.icons.filled.Star
import androidx.compose.material.icons.filled.StarBorder
import androidx.compose.material.icons.filled.StarHalf
import androidx.compose.material.icons.filled.Visibility
import androidx.compose.material3.Icon
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Surface
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import kotlin.math.floor
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.tooling.preview.Preview
import coil.compose.AsyncImagePainter
import coil.compose.rememberAsyncImagePainter
import com.thyview.models.reviews.Post
import com.thyview.ui.ImdbDarkGray
import com.thyview.ui.ImdbYellow
import com.thyview.ui.ThyViewTheme
import java.time.Instant


/**
 * A reusable component to display star ratings
 *
 * @param rating The rating value, expected to be a string like "4.5"
 * @param maxStars The maximum number of stars to display, defaults to 5
 * @param starSize The size of each star icon in dp
 * @param starSpacing The space between stars in dp
 * @param filledStarColor The color of filled stars
 * @param unfilledStarColor The color of unfilled stars
 * @param showRatingText Whether to display the numeric rating text
 * @param modifier Optional modifier for the component
 */
@Composable
fun RatingStars(
    rating: String,
    maxStars: Int = 5,
    starSize: Int = 16,
    starSpacing: Int = 2,
    filledStarColor: androidx.compose.ui.graphics.Color = ImdbYellow,
    unfilledStarColor: androidx.compose.ui.graphics.Color = MaterialTheme.colorScheme.surfaceVariant,
    showRatingText: Boolean = false,
    modifier: Modifier = Modifier
) {
    // Parse the rating string to a float, defaulting to 0 if invalid
    val numericRating = rating.toFloatOrNull() ?: 0f
    // Ensure the rating is clamped between 0 and maxStars
    val clampedRating = numericRating.coerceIn(0f, maxStars.toFloat())

    // Calculate the number of full and partial stars
    val fullStars = floor(clampedRating).toInt()
    val hasHalfStar = clampedRating - fullStars >= 0.5f && clampedRating - fullStars < 1.0f

    Row(
        verticalAlignment = Alignment.CenterVertically,
        modifier = modifier
    ) {
        // Draw the stars
        for (i in 1..maxStars) {
            when {
                i <= fullStars -> {
                    // Filled star
                    Icon(
                        Icons.Default.Star,
                        contentDescription = "Filled Star",
                        tint = filledStarColor,
                        modifier = Modifier.size(starSize.dp)
                    )
                }
                i == fullStars + 1 && hasHalfStar -> {
                    // Half star
                    Icon(
                        Icons.Default.StarHalf,
                        contentDescription = "Half Star",
                        tint = filledStarColor,
                        modifier = Modifier.size(starSize.dp)
                    )
                }
                else -> {
                    // Empty star
                    Icon(
                        Icons.Default.StarBorder,
                        contentDescription = "Empty Star",
                        tint = unfilledStarColor,
                        modifier = Modifier.size(starSize.dp)
                    )
                }
            }

            // Add spacing between stars except after the last one
            if (i < maxStars) {
                Spacer(modifier = Modifier.width(starSpacing.dp))
            }
        }

        // Optionally show the numeric rating
        if (showRatingText) {
            Spacer(modifier = Modifier.width(4.dp))
            Text(
                text = rating,
                color = MaterialTheme.colorScheme.onSurface,
                fontSize = 12.sp,
                fontWeight = FontWeight.Bold
            )
        }
    }
}

// Preview for the RatingStars component
@Preview(showBackground = true)
@Composable
fun RatingStarsPreview() {
    ThyViewTheme {
        Surface(color = MaterialTheme.colorScheme.surface, modifier = Modifier.padding(16.dp)) {
            Column(verticalArrangement = Arrangement.spacedBy(8.dp)) {
                // Various rating examples
                RatingStars(rating = "5.0", showRatingText = true)
                RatingStars(rating = "4.5", showRatingText = true) // Half star
                RatingStars(rating = "3.0", showRatingText = true)
                RatingStars(rating = "2.5", showRatingText = true)
                RatingStars(rating = "1.0", showRatingText = true)

                // Custom styling example
                RatingStars(
                    rating = "3.5",
                    maxStars = 5,
                    starSize = 24,
                    starSpacing = 4,
                    filledStarColor = ImdbYellow,
                    unfilledStarColor = MaterialTheme.colorScheme.surfaceVariant,
                    showRatingText = true
                )
            }
        }
    }
}

@Composable
fun PostItemCard(
    post: Post,
    onLike: () -> Unit,
    onComment: () -> Unit
) {
    Column(
        modifier = Modifier
            .fillMaxWidth()
            .background(ImdbDarkGray) // MAIN REVIEW CARD BACKGROUND
            .padding(20.dp)
    ) {
        // Main content row with movie image, content, and user info
        Row(
            modifier = Modifier.fillMaxWidth(),
            horizontalArrangement = Arrangement.spacedBy(12.dp)
        ) {
            // Left side: Movie image
            post.imageUrl?.let { url ->
                val painter = rememberAsyncImagePainter(
                    model = url,
                    error = null // This will hide the image if it fails to load
                )

                // Check if the painter state is not an error
                if (painter.state !is AsyncImagePainter.State.Error) {
                    Image(
                        painter = painter,
                        contentDescription = null,
                        modifier = Modifier
                            .size(80.dp)
                            .clip(RoundedCornerShape(8.dp))
                    )
                }
            }

            // Center: Title, rating, and content
            Column(
                modifier = Modifier.weight(1f)
            ) {
                // Title
                Text(
                    text = post.title,
                    color = MaterialTheme.colorScheme.onSurface,
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold
                )

                Spacer(modifier = Modifier.height(4.dp))

                // Rating stars
                RatingStars(
                    rating = post.rating.toString(),
                    starSize = 16,
                    starSpacing = 2,
                    filledStarColor = ImdbYellow,
                    unfilledStarColor = MaterialTheme.colorScheme.surfaceVariant,
                    showRatingText = false
                )

                Spacer(modifier = Modifier.height(8.dp))

                // Content text
                Text(
                    text = post.content,
                    color = MaterialTheme.colorScheme.onSurface,
                    fontSize = 14.sp
                )
            }

            // Right side: User info (profile image, username, timestamp)
            Column(
                horizontalAlignment = Alignment.End
            ) {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    // Profile image
                    Image(
                        painter = rememberAsyncImagePainter(
                            model = post.profileImageUrl,
                            error = painterResource(id = R.drawable.profile_user)
                        ),
                        contentDescription = null,
                        modifier = Modifier
                            .size(24.dp)
                            .clip(CircleShape)
                    )

                    Spacer(modifier = Modifier.width(8.dp))

                    // Username
                    Text(
                        text = post.username,
                        color = MaterialTheme.colorScheme.onSurfaceVariant,
                        fontSize = 12.sp
                    )
                }

                Spacer(modifier = Modifier.height(8.dp))

                // Timestamp aligned with end of username
                Text(
                    text = formatDate(post.createdAt),
                    color = MaterialTheme.colorScheme.onSurfaceVariant,
                    fontSize = 10.sp
                )
            }
        }

        Spacer(modifier = Modifier.height(24.dp))

        // Interaction buttons
        Row(horizontalArrangement = Arrangement.SpaceBetween, modifier = Modifier.fillMaxWidth()) {
            Row(modifier = Modifier.clickable { onLike() }, verticalAlignment = Alignment.CenterVertically) {
                Icon(
                    if (post.liked) Icons.Default.Favorite else Icons.Default.FavoriteBorder,
                    contentDescription = "Like",
                    tint = if (post.liked) MaterialTheme.colorScheme.primary else MaterialTheme.colorScheme.onSurface
                )
                Spacer(modifier = Modifier.width(4.dp))
                Text("${post.likeCount}", color = MaterialTheme.colorScheme.onSurface)
            }
            Row(
                modifier = Modifier.clickable { onComment() },
                verticalAlignment = Alignment.CenterVertically
            ) {
                Icon(
                    Icons.Default.Comment,
                    contentDescription = "Comment",
                    tint = MaterialTheme.colorScheme.onSurface
                )
                Spacer(modifier = Modifier.width(4.dp))
                Text("${post.commentCount}", color = MaterialTheme.colorScheme.onSurface)
            }
            //setting the views gone for now
            if (false) {
                Row(verticalAlignment = Alignment.CenterVertically) {
                    Icon(
                        Icons.Default.Visibility,
                        contentDescription = "Views",
                        tint = MaterialTheme.colorScheme.onSurface
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Text("${post.viewCount}", color = MaterialTheme.colorScheme.onSurface)
                }
            }
        }
    }
}

// Helper function to format date string
fun formatDate(dateString: String): String {
    // This is a placeholder. Implement actual date formatting logic
    // based on your app's requirements
    return try {
        val instant = java.time.Instant.parse(dateString)
        val now = java.time.Instant.now()
        val diffSeconds = java.time.Duration.between(instant, now).seconds

        when {
            diffSeconds < 60 -> "Just now"
            diffSeconds < 3600 -> "${diffSeconds / 60} min ago"
            diffSeconds < 86400 -> "${diffSeconds / 3600} hours ago"
            else -> "${diffSeconds / 86400} days ago"
        }
    } catch (e: Exception) {
        dateString
    }
}

@Preview(showBackground = true, widthDp = 360)
@Composable
fun PostItemCardPreview() {
    // Create a sample Post for the preview that matches the actual Post model
    val samplePost = Post(
        id = "1",
        externalAuthorId = "JohnDoe",
        username = "anto",
        profileImageUrl = "", // Empty URL to test placeholder
        title = "28 Years Later",
        rating = 4.0f,
        content = "the most terrifying and unsettling thing about this movie was seeing those fucking Teletubbies",
        imageUrl = "https://images.unsplash.com/photo-1517248135467-4c7edcad34c4", // This might not load in preview
        likeCount = 24,
        commentCount = 5,
        viewCount = 128,
        createdAt = Instant.now().minusSeconds(7200).toString(),
        liked = true
    )

    // Use ThyViewTheme to provide the theme colors
    ThyViewTheme {
        PostItemCard(
            post = samplePost,
            onLike = { /* Preview doesn't need real actions */ },
            onComment = { /* Preview doesn't need real actions */ }
        )
    }
}

// Optional: Add multiple previews with different configurations
@Preview(showBackground = true, widthDp = 360, name = "Post Without Image")
@Composable
fun PostItemCardWithoutImagePreview() {
    val samplePost = Post(
        id = "2",
        externalAuthorId = "JaneDoe",
        username = "JaneDoe",
        profileImageUrl = "", // Empty URL to test placeholder
        title = "Avengers: Endgame",
        rating = 4.5f,
        content = "The staff went above and beyond to make sure my experience was perfect. Would definitely recommend!",
        imageUrl = null, // No image for this preview
        likeCount = 45,
        commentCount = 12,
        viewCount = 67,
        createdAt = Instant.now().toString(),
        liked = false
    )

    ThyViewTheme {
        PostItemCard(
            post = samplePost,
            onLike = { /* Preview doesn't need real actions */ },
            onComment = { /* Preview doesn't need real actions */ }
        )
    }
}
