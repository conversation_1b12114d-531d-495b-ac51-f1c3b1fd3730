package com.thyview.ui.reviews

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Close
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.KeyboardCapitalization
import androidx.compose.ui.text.input.KeyboardType
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavController
import com.thyview.R
import com.thyview.ui.ThyViewTheme
import com.thyview.ui.ImdbBlack
import com.thyview.ui.ImdbYellow
import com.thyview.ui.ImdbWhite
import com.thyview.ui.ImdbDarkGray
import com.thyview.ui.ImdbMediumGray

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun AddReviewScreen(
    movieTitle: String? = null,
    navController: NavController,
    viewModel: AddReviewViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsState()
    
    LaunchedEffect(uiState.isSubmitted) {
        if (uiState.isSubmitted) {
            navController.navigateUp()
        }
    }

    ThyViewTheme {
        Scaffold(
            topBar = {
                TopAppBar(
                    title = { 
                        Text(
                            text = "Add Review",
                            color = ImdbYellow
                        ) 
                    },
                    navigationIcon = {
                        IconButton(onClick = { navController.navigateUp() }) {
                            Icon(
                                imageVector = Icons.Default.Close,
                                contentDescription = "Close",
                                tint = ImdbYellow
                            )
                        }
                    },
                    colors = TopAppBarDefaults.topAppBarColors(
                        containerColor = ImdbBlack,
                        titleContentColor = ImdbYellow
                    )
                )
            },
            containerColor = ImdbBlack
        ) { innerPadding ->
            val scrollState = rememberScrollState()

            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(innerPadding)
                    .background(ImdbBlack)
                    .verticalScroll(scrollState)
                    .padding(16.dp)
                    .padding(bottom = 32.dp), // Extra bottom padding to prevent button cutoff
                verticalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                // Movie title (if provided)
                movieTitle?.let { title ->
                    Card(
                        modifier = Modifier.fillMaxWidth(),
                        colors = CardDefaults.cardColors(containerColor = ImdbDarkGray)
                    ) {
                        Column(
                            modifier = Modifier.padding(16.dp)
                        ) {
                            Text(
                                text = "Reviewing:",
                                color = ImdbWhite.copy(alpha = 0.7f),
                                fontSize = 12.sp
                            )
                            Spacer(modifier = Modifier.height(4.dp))
                            Text(
                                text = title,
                                color = ImdbWhite,
                                fontSize = 16.sp,
                                fontWeight = FontWeight.Medium
                            )
                        }
                    }
                }

                // Rating section
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    colors = CardDefaults.cardColors(containerColor = ImdbDarkGray)
                ) {
                    Column(
                        modifier = Modifier.padding(16.dp)
                    ) {
                        Text(
                            text = "Your Rating",
                            color = ImdbWhite,
                            fontSize = 16.sp,
                            fontWeight = FontWeight.Medium
                        )
                        Spacer(modifier = Modifier.height(12.dp))

                        InteractiveRatingStars(
                            rating = uiState.rating,
                            onRatingChanged = viewModel::updateRating,
                            starSize = 36,
                            starSpacing = 6,
                            showRatingText = true
                        )
                    }
                }

                // Review text field
                Card(
                    modifier = Modifier.fillMaxWidth(),
                    colors = CardDefaults.cardColors(containerColor = ImdbDarkGray)
                ) {
                    Column(
                        modifier = Modifier.padding(16.dp)
                    ) {
                        Text(
                            text = "Your Review",
                            color = ImdbWhite,
                            fontSize = 16.sp,
                            fontWeight = FontWeight.Medium
                        )
                        Spacer(modifier = Modifier.height(8.dp))
                        
                        OutlinedTextField(
                            value = uiState.reviewText,
                            onValueChange = viewModel::updateReviewText,
                            modifier = Modifier
                                .fillMaxWidth()
                                .height(320.dp),
                            placeholder = {
                                Text(
                                    "Share your thoughts about this movie... (minimum 150 characters)",
                                    color = ImdbWhite.copy(alpha = 0.6f)
                                )
                            },
                            colors = TextFieldDefaults.colors(
                                focusedTextColor = ImdbWhite,
                                unfocusedTextColor = ImdbWhite,
                                cursorColor = ImdbYellow,
                                focusedIndicatorColor = ImdbBlack,
                                unfocusedIndicatorColor = ImdbBlack,
                                focusedContainerColor = ImdbBlack,
                                unfocusedContainerColor = ImdbBlack,
                                focusedPlaceholderColor = ImdbWhite.copy(alpha = 0.6f),
                                unfocusedPlaceholderColor = ImdbWhite.copy(alpha = 0.6f)
                            ),
                            keyboardOptions = KeyboardOptions(
                                capitalization = KeyboardCapitalization.Sentences,
                                keyboardType = KeyboardType.Text
                            ),
                            maxLines = 15
                        )
                        
                        Spacer(modifier = Modifier.height(8.dp))
                        
                        // Character count
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.SpaceBetween,
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Text(
                                text = "${uiState.reviewText.length}/150 characters minimum",
                                color = if (uiState.reviewText.length >= 150) ImdbYellow else ImdbWhite.copy(alpha = 0.7f),
                                fontSize = 12.sp
                            )
                            
                            if (uiState.reviewText.length >= 150) {
                                Text(
                                    text = "✓ Ready to submit",
                                    color = ImdbWhite,
                                    fontSize = 12.sp,
                                    fontWeight = FontWeight.Medium
                                )
                            }
                        }
                    }
                }

                // Submit button
                Button(
                    onClick = {
                        viewModel.submitReview(movieTitle)
                    },
                    enabled = uiState.reviewText.length >= 150 && !uiState.isSubmitting,
                    modifier = Modifier.fillMaxWidth(),
                    colors = ButtonDefaults.buttonColors(
                        containerColor = if (uiState.reviewText.length >= 150) ImdbYellow else ImdbMediumGray,
                        contentColor = ImdbBlack
                    )
                ) {
                    if (uiState.isSubmitting) {
                        CircularProgressIndicator(
                            modifier = Modifier.size(20.dp),
                            color = ImdbBlack,
                            strokeWidth = 2.dp
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text(
                            text = "Submitting...",
                            fontWeight = FontWeight.Medium
                        )
                    } else {
                        Text(
                            text = "Submit Review",
                            fontWeight = FontWeight.Medium
                        )
                    }
                }

                // Error message
                uiState.error?.let { error ->
                    Card(
                        modifier = Modifier.fillMaxWidth(),
                        colors = CardDefaults.cardColors(containerColor = MaterialTheme.colorScheme.errorContainer)
                    ) {
                        Text(
                            text = error,
                            color = MaterialTheme.colorScheme.onErrorContainer,
                            modifier = Modifier.padding(16.dp),
                            fontSize = 14.sp
                        )
                    }
                }

            }
        }
    }
}
