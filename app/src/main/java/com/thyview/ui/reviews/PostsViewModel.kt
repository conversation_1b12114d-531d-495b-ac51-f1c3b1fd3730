package com.thyview.ui.reviews

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.flow.update
import kotlinx.coroutines.launch
import javax.inject.Inject
import com.thyview.models.reviews.Post
import com.thyview.services.AWSPostService
import timber.log.Timber

@HiltViewModel
class PostsViewModel @Inject constructor(
    private val postService: AWSPostService
) : ViewModel() {

    private val _uiState = MutableStateFlow(PostsUiState())
    val uiState: StateFlow<PostsUiState> = _uiState.asStateFlow()

    private var lastCreatedAt: String? = null

    init {
        Timber.d("PostsViewModel initialized with postService=${postService.javaClass.simpleName}")
        Timber.d("Mock data enabled: ${postService.useMockData}")
        loadMorePosts()
    }

    fun loadMorePosts() {
        if (_uiState.value.isLoading) {
            Timber.d("Skipping loadMorePosts - already loading")
            return
        }
        
        // Set loading state
        _uiState.update { it.copy(isLoading = true) }
        Timber.d("Loading posts with lastCreatedAt=$lastCreatedAt")

        viewModelScope.launch {
            try {
                val posts = postService.getPosts(lastCreatedAt)
                Timber.d("Loaded ${posts.size} posts")
                
                if (posts.isNotEmpty()) {
                    Timber.d("Received posts: ${posts.map { it.id }}")
                    lastCreatedAt = posts.last().createdAt
                    _uiState.update { it.copy(
                        posts = it.posts + posts,
                        isLoading = false,
                        error = null
                    ) }
                    Timber.d("Updated UI state, total posts: ${_uiState.value.posts.size}")
                } else {
                    // Handle case where no posts are returned
                    Timber.d("No posts returned")
                    _uiState.update { it.copy(
                        isLoading = false,
                        error = if (it.posts.isEmpty()) "No posts available" else null
                    ) }
                }
            } catch (e: Exception) {
                Timber.e(e, "Error loading posts")
                _uiState.update { it.copy(
                    isLoading = false,
                    error = e.message ?: "Failed to load posts"
                ) }
            }
        }
    }

    fun toggleLike(postId: String) {
        Timber.d("Attempting to toggle like for post $postId")
        viewModelScope.launch {
            try {
                val liked = postService.likePost(postId)
                Timber.d("Toggle like for post $postId: now liked=$liked")
                
                // Update the post in the UI
                val updatedPosts = _uiState.value.posts.map { post ->
                    if (post.id == postId) {
                        val newLikeCount = if (liked) post.likeCount + 1 else post.likeCount - 1
                        Timber.d("Updating post $postId: liked=$liked, likeCount=$newLikeCount")
                        post.copy(liked = liked, likeCount = newLikeCount)
                    } else {
                        post
                    }
                }
                
                _uiState.update { it.copy(posts = updatedPosts, error = null) }
                Timber.d("UI state updated after like toggle")
            } catch (e: Exception) {
                Timber.e(e, "Error toggling like for post $postId")
                _uiState.update { it.copy(error = e.message ?: "Failed to like post") }
            }
        }
    }

    fun refreshPosts() {
        Timber.d("Refreshing posts - clearing existing data")
        lastCreatedAt = null
        _uiState.update { it.copy(posts = emptyList(), error = null, isLoading = true) }
        Timber.d("State reset, now loading fresh posts")
        loadMorePosts()
    }
}

data class PostsUiState(
    val posts: List<Post> = emptyList(),
    val error: String? = null,
    val isLoading: Boolean = false
)
