package com.thyview.ui.reviews

import android.net.Uri
import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.thyview.repository.PostRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.launch
import kotlinx.coroutines.withContext
import java.io.File
import java.io.IOException
import java.util.UUID
import javax.inject.Inject
import okhttp3.MediaType.Companion.toMediaTypeOrNull
import okhttp3.OkHttpClient
import okhttp3.Request
import okhttp3.RequestBody.Companion.asRequestBody

@HiltViewModel
class ImageUploadViewModel @Inject constructor(
    private val socialRepository: PostRepository,
    private val okHttpClient: OkHttpClient
) : ViewModel() {

    private val _isUploading = MutableLiveData(false)
    val isUploading: LiveData<Boolean> = _isUploading

    private val _uploadProgress = MutableLiveData(0)
    val uploadProgress: LiveData<Int> = _uploadProgress

    private val _uploadedImageUrl = MutableLiveData<String?>(null)
    val uploadedImageUrl: LiveData<String?> = _uploadedImageUrl

    private val _error = MutableLiveData<String?>(null)
    val error: LiveData<String?> = _error

    /**
     * Uploads an image from a local file to storage
     */
    fun uploadImage(
        imageFile: File,
        contentType: String,
        onSuccess: (String) -> Unit,
        onError: (String) -> Unit
    ) {
        if (!imageFile.exists()) {
            onError("Image file does not exist")
            return
        }

        viewModelScope.launch {
            try {
                _isUploading.value = true
                _uploadProgress.value = 0
                _error.value = null

                // Generate a unique filename
                val fileExtension = contentType.split("/").lastOrNull() ?: "jpg"
                val fileName = "image_${UUID.randomUUID()}.$fileExtension"

                // Get signed upload URL
                val uploadInfo = withContext(Dispatchers.IO) {
                    socialRepository.getImageUploadUrl(fileName, contentType)
                }

                if (uploadInfo == null) {
                    onError("Failed to get upload URL")
                    return@launch
                }

                val (uploadUrl, publicUrl) = uploadInfo

                // Upload the file
                val success = withContext(Dispatchers.IO) {
                    uploadFileToSignedUrl(imageFile, uploadUrl, contentType)
                }

                if (success) {
                    _uploadedImageUrl.value = publicUrl
                    onSuccess(publicUrl)
                } else {
                    onError("Failed to upload image")
                }
            } catch (e: Exception) {
                onError("Error uploading image: ${e.localizedMessage}")
            } finally {
                _isUploading.value = false
                _uploadProgress.value = 100
            }
        }
    }

    /**
     * Uploads an image from a content URI to storage
     */
    fun uploadImageFromUri(
        imageUri: Uri,
        contentType: String,
        cacheDir: File,
        onSuccess: (String) -> Unit,
        onError: (String) -> Unit
    ) {
        viewModelScope.launch {
            try {
                _isUploading.value = true
                _uploadProgress.value = 0
                _error.value = null

                // Copy URI to a temporary file
                val tempFile = withContext(Dispatchers.IO) {
                    val fileExtension = contentType.split("/").lastOrNull() ?: "jpg"
                    val tempFile = File(cacheDir, "upload_${UUID.randomUUID()}.$fileExtension")
                    
                    try {
                        tempFile.outputStream().use { outputStream ->
                            imageUri.toFile().inputStream().use { inputStream ->
                                inputStream.copyTo(outputStream)
                            }
                        }
                        tempFile
                    } catch (e: Exception) {
                        throw IOException("Failed to copy image file: ${e.message}")
                    }
                }

                // Upload the file
                uploadImage(tempFile, contentType, onSuccess) { error ->
                    onError(error)
                    tempFile.delete()
                }
            } catch (e: Exception) {
                onError("Error processing image: ${e.localizedMessage}")
                _isUploading.value = false
            }
        }
    }

    /**
     * Helper function to upload a file to a signed URL
     */
    private fun uploadFileToSignedUrl(file: File, signedUrl: String, contentType: String): Boolean {
        return try {
            val mediaType = contentType.toMediaTypeOrNull()
            val requestBody = file.asRequestBody(mediaType)

            val request = Request.Builder()
                .url(signedUrl)
                .put(requestBody)
                .build()

            val response = okHttpClient.newCall(request).execute()
            response.isSuccessful
        } catch (e: Exception) {
            false
        }
    }

    /**
     * Clears the current uploaded image URL
     */
    fun clearUploadedImage() {
        _uploadedImageUrl.value = null
        _uploadProgress.value = 0
    }
}

// Extension function to convert Uri to File
private fun Uri.toFile(): File {
    val path = this.path ?: throw IOException("Invalid URI path")
    return File(path)
}