package com.thyview.ui.reviews

import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavController
import com.thyview.models.reviews.Comment
import android.util.Log
import androidx.compose.foundation.clickable
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.ThumbUp
import androidx.compose.material.icons.outlined.ThumbUp
import com.thyview.ui.ImdbDarkGray
import com.thyview.ui.ImdbWhite
import com.thyview.ui.ThyViewTheme

/**
 * Screen for displaying replies to a specific comment within a post.
 * Shows the parent comment at the top and its replies below.
 *
 * @param postId The ID of the post containing the comment
 * @param commentId The ID of the parent comment whose replies we're showing
 * @param navController Navigation controller to handle back navigation
 * @param viewModel ViewModel for managing comment data, injected by Hilt
 */
@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun CommentRepliesScreen(
    postId: String,
    commentId: String,
    navController: NavController,
    viewModel: CommentViewModel = hiltViewModel()
) {
    // Apply ThyViewTheme to the entire screen
    ThyViewTheme {
        val uiState by viewModel.uiState.collectAsState()
        var parentComment by remember { mutableStateOf<Comment?>(null) }

        // Set parent comment ID to load specific replies and fetch parent comment
        LaunchedEffect(commentId) {
            viewModel.setParentCommentId(commentId)
            // Fetch the parent comment to display at the top
            parentComment = viewModel.getCommentById(commentId)
            viewModel.loadComments() // Load replies only
        }

        // Use Scaffold to properly structure the screen with a top app bar
        Scaffold(
            topBar = {
                // Custom toolbar with back arrow
                TopAppBar(
                    title = { Text("Replies") },
                    navigationIcon = {
                        IconButton(onClick = { navController.popBackStack() }) {
                            Icon(
                                imageVector = Icons.Default.ArrowBack,
                                contentDescription = "Back"
                            )
                        }
                    },
                    colors = TopAppBarDefaults.topAppBarColors(
                        containerColor = MaterialTheme.colorScheme.surface,
                        titleContentColor = MaterialTheme.colorScheme.onSurface
                    )
                )
            },
            // Content area
            content = { padding ->
                Box(
                    modifier = Modifier
                        .fillMaxSize()
                        .padding(padding)
                ) {
                    Column(
                        modifier = Modifier
                            .fillMaxSize()
                            .padding(bottom = 72.dp) // Add padding for the input bar
                    ) {
                        // Display parent comment as the "post" at the top
                        parentComment?.let { comment ->
                            // Create a custom parent comment display that looks like a post
                            ParentCommentCard(
                                comment = comment,
                                onLike = { likedComment ->
                                    // Handle like action for parent comment
                                    Log.d("CommentRepliesScreen", "Like clicked on parent comment: ${likedComment.id}")
                                    viewModel.likeComment(likedComment.id)
                                },
                                modifier = Modifier.fillMaxWidth()
                            )
                        } ?: run {
                            // Show loading state when parent comment is null
                            Box(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .height(120.dp),
                                contentAlignment = Alignment.Center
                            ) {
                                CircularProgressIndicator()
                            }
                        }

                        Divider(color = MaterialTheme.colorScheme.surfaceVariant)

                        // Display replies
                        LazyColumn(modifier = Modifier.weight(1f)) {
                            items(uiState.comments) { reply ->
                                // Added debugging for the reply
                                Log.d("CommentRepliesScreen", "Rendering reply: id=${reply.id}, liked=${reply.liked}, likeCount=${reply.likeCount}")
                                
                                // Use CommentItemCard for replies but now include the onLike callback
                                CommentItemCard(
                                    comment = reply,
                                    repliesCount = 0, // Don't show replies count for replies
                                    onReply = { }, // Empty handler since we're hiding the option
                                    onLike = { likedComment -> 
                                        // Handle like action
                                        Log.d("CommentRepliesScreen", "Like clicked on reply: ${likedComment.id}")
                                        viewModel.likeComment(likedComment.id)
                                    },
                                    onViewReplies = null, // Passing null hides the replies option
                                    hideReplyOption = true, // Hide the Reply option in replies view
                                    modifier = Modifier.fillMaxWidth()
                                )
                                Divider(
                                    color = Color.DarkGray,
                                    modifier = Modifier.padding(horizontal = 18.dp)
                                )
                            }
                        }
                    }

                    // Comment input bar at the bottom
                    CommentInputBar(
                        value = uiState.commentText,
                        onValueChange = viewModel::onCommentTextChange,
                        onSend = { text, imageUri ->
                            viewModel.submitComment()
                        },
                        modifier = Modifier
                            .align(Alignment.BottomCenter)
                            .fillMaxWidth()
                    )
                }
            }
        )
    }
}

/**
 * A special card for displaying the parent comment at the top of the replies screen
 * Now includes like functionality
 */
@Composable
fun ParentCommentCard(
    comment: Comment,
    onLike: (Comment) -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier,
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
        colors = CardDefaults.cardColors(
            containerColor = ImdbDarkGray, // Use theme colors
            contentColor = ImdbWhite
        ),
        shape = RoundedCornerShape(0.dp)
    ) {
        Column(
            modifier = Modifier
                .padding(16.dp)
                .fillMaxWidth()
        ) {
            // Comment header with username, label and date
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Row(verticalAlignment = Alignment.CenterVertically) {
                    Text(
                        text = comment.username,
                        style = MaterialTheme.typography.titleMedium,
                        color = MaterialTheme.colorScheme.onSurface
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Surface(
                        shape = RoundedCornerShape(4.dp),
                        color = MaterialTheme.colorScheme.primary.copy(alpha = 0.2f)
                    ) {
                        Text(
                            text = "",
                            style = MaterialTheme.typography.labelSmall,
                            color = MaterialTheme.colorScheme.primary,
                            modifier = Modifier.padding(horizontal = 6.dp, vertical = 2.dp)
                        )
                    }
                }
                
                Text(
                    text = comment.date,
                    style = MaterialTheme.typography.bodySmall,
                    color = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.7f)
                )
            }

            Spacer(modifier = Modifier.height(12.dp))

            // Comment body text
            Text(
                text = comment.text,
                style = MaterialTheme.typography.bodyMedium,
                modifier = Modifier.fillMaxWidth(),
                color = MaterialTheme.colorScheme.onSurface
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // Add Like section
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                // Like button with counter
                Row(
                    modifier = Modifier
                        .clickable { 
                            Log.d("ParentCommentCard", "Like button clicked for parent comment: ${comment.id}")
                            onLike(comment) 
                        }
                        .padding(vertical = 8.dp, horizontal = 8.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        imageVector = if (comment.liked) Icons.Filled.ThumbUp else Icons.Outlined.ThumbUp,
                        contentDescription = if (comment.liked) "Unlike comment" else "Like comment",
                        tint = if (comment.liked) MaterialTheme.colorScheme.primary else MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.7f),
                        modifier = Modifier.size(18.dp)
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Text(
                        text = if (comment.likeCount == 1) "1 like" else "${comment.likeCount} likes",
                        style = MaterialTheme.typography.bodySmall,
                        color = MaterialTheme.colorScheme.onSurfaceVariant.copy(alpha = 0.7f)
                    )
                }
            }
        }
    }
}
