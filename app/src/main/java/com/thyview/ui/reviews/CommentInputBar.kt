package com.thyview.ui.reviews

import android.net.Uri
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.contract.ActivityResultContracts.GetContent
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.AttachFile
import androidx.compose.material.icons.filled.Send
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewParameter
import androidx.compose.ui.tooling.preview.PreviewParameterProvider
import androidx.compose.ui.unit.dp
import coil.compose.rememberAsyncImagePainter

/**
 * A comment input bar with:
 *  - Text field
 *  - Attach-image button (opens gallery)
 *  - Thumbnail preview of selected image
 *  - Send button
 *
 * @param value The current text of the comment
 * @param onValueChange Called when text changes
 * @param onSend Called when Send is tapped (text, optional imageUri)
 * @param modifier Optional modifier for the component
 */
@Composable
fun CommentInputBar(
    value: String,
    onValueChange: (String) -> Unit,
    onSend: (text: String, imageUri: Uri?) -> Unit,
    modifier: Modifier = Modifier
) {
    // State for the selected image URI
    var imageUri by remember { mutableStateOf<Uri?>(null) }

    // Launcher to pick an image from gallery
    val launcher = rememberLauncherForActivityResult(GetContent()) { uri: Uri? ->
        imageUri = uri
    }

    Surface(
        modifier = modifier
            .fillMaxWidth()
            .wrapContentHeight(),
        color = Color(0xFF121212) // Dark background for the input bar
    ) {
        Column {
            // If an image is selected, show a thumbnail with a remove button
            imageUri?.let { uri ->
                Row(
                    verticalAlignment = Alignment.CenterVertically,
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 16.dp, vertical = 8.dp)
                ) {
                    Image(
                        painter = rememberAsyncImagePainter(uri),
                        contentDescription = "Attached image",
                        modifier = Modifier
                            .size(64.dp)
                            .clip(RoundedCornerShape(8.dp))
                    )
                    Spacer(Modifier.width(8.dp))
                    TextButton(onClick = { imageUri = null }) {
                        Text("Remove", color = Color.Red)
                    }
                }
            }

            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp, vertical = 8.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                // Text field
                TextField(
                    value = value,
                    onValueChange = onValueChange,
                    modifier = Modifier
                        .weight(1f)
                        .height(56.dp)
                        .clip(RoundedCornerShape(28.dp)),
                    placeholder = { 
                        Text(
                            "Add a comment...",
                            color = Color.Gray
                        )
                    },
                    colors = TextFieldDefaults.colors(
                        focusedContainerColor = Color(0xFF1E1E1E),
                        unfocusedContainerColor = Color(0xFF1E1E1E),
                        focusedTextColor = Color.White,
                        unfocusedTextColor = Color.White,
                        focusedIndicatorColor = Color.Transparent,
                        unfocusedIndicatorColor = Color.Transparent
                    ),
                    singleLine = false,
                    maxLines = 3,
                    trailingIcon = {
                        IconButton(onClick = { launcher.launch("image/*") }) {
                            Icon(
                                imageVector = Icons.Default.AttachFile,
                                contentDescription = "Attach image",
                                tint = Color.Gray
                            )
                        }
                    }
                )
                
                Spacer(modifier = Modifier.width(8.dp))
                
                // Send button
                Box(
                    modifier = Modifier
                        .size(48.dp)
                        .clip(CircleShape)
                        .background(
                            if (value.isNotBlank() || imageUri != null) MaterialTheme.colorScheme.primary 
                            else Color(0xFF444444)
                        ),
                    contentAlignment = Alignment.Center
                ) {
                    IconButton(
                        onClick = { 
                            onSend(value.trim(), imageUri)
                            onValueChange("")
                            imageUri = null
                        },
                        enabled = value.isNotBlank() || imageUri != null
                    ) {
                        Icon(
                            imageVector = Icons.Default.Send,
                            contentDescription = "Send comment",
                            tint = Color.White
                        )
                    }
                }
            }
        }
    }
}

/**
 * Preview for CommentInputBar in empty state
 */
@Preview(
    name = "Comment Input Bar - Empty",
    showBackground = true,
    backgroundColor = 0xFF121212
)
@Composable
fun CommentInputBarEmptyPreview() {
    CommentInputBar(
        value = "",
        onValueChange = {},
        onSend = { _, _ -> }
    )
}

/**
 * Preview for CommentInputBar with text entered
 */
@Preview(
    name = "Comment Input Bar - With Text",
    showBackground = true,
    backgroundColor = 0xFF121212
)
@Composable
fun CommentInputBarWithTextPreview() {
    CommentInputBar(
        value = "This is a sample comment to preview how text looks in the input field",
        onValueChange = {},
        onSend = { _, _ -> }
    )
}

/**
 * Preview for CommentInputBar with image
 */
@Preview(
    name = "Comment Input Bar - With Image",
    showBackground = true,
    backgroundColor = 0xFF121212
)
@Composable
fun CommentInputBarWithImagePreview() {
    // Note: In a real preview, we can't actually show a selected image
    // This just demonstrates the UI layout with text
    CommentInputBar(
        value = "Comment with image",
        onValueChange = {},
        onSend = { _, _ -> }
    )
}

/**
 * Provider of different text values for comment input
 */
class CommentTextProvider : PreviewParameterProvider<String> {
    override val values: Sequence<String> = sequenceOf(
        "",
        "Short comment",
        "This is a longer comment that might need to be truncated in the UI"
    )
}

/**
 * Parameterized preview for different text values
 */
@Preview(
    name = "Comment Input Bar - Various Texts",
    showBackground = true,
    backgroundColor = 0xFF121212
)
@Composable
fun CommentInputBarParameterizedPreview(
    @PreviewParameter(CommentTextProvider::class) commentText: String
) {
    CommentInputBar(
        value = commentText,
        onValueChange = {},
        onSend = { _, _ -> }
    )
}
