
package com.thyview.ui.reviews

import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import com.thyview.ui.ImdbBlack
import com.thyview.ui.ImdbDarkGray
import com.thyview.ui.ImdbYellow
import com.thyview.ui.ImdbWhite
import com.thyview.ui.ImdbLightGray
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.tooling.preview.PreviewParameter
import androidx.compose.ui.tooling.preview.PreviewParameterProvider
import androidx.compose.ui.unit.dp
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Reply
import androidx.compose.material.icons.filled.KeyboardArrowDown
import androidx.compose.material.icons.filled.KeyboardArrowRight
import androidx.compose.material.icons.filled.ThumbUp
import androidx.compose.material.icons.outlined.ThumbUp
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.text.style.TextOverflow
import com.thyview.models.reviews.Comment
import com.thyview.ui.ThyViewTheme
import android.util.Log

/**
 * CommentItemCard displays a single comment with options to reply, like and navigate to replies
 * Styled to match IMDB's dark theme with yellow accents
 *
 * @param comment The comment data to display
 * @param repliesCount Number of replies to this comment
 * @param onReply Callback when user wants to reply to this comment
 * @param onLike Callback when user wants to like this comment
 * @param onViewReplies Callback when user wants to view replies of this comment
 * @param isRepliesLoading Whether replies are currently being loaded
 * @param hideReplyOption Whether to hide the Reply option (useful in CommentRepliesScreen)
 * @param modifier Optional modifier for styling
 */
@Composable
fun CommentItemCard(
    comment: Comment,
    repliesCount: Int = 0,
    onReply: (Comment) -> Unit,
    onLike: ((Comment) -> Unit)? = null,
    onViewReplies: ((Comment) -> Unit)? = null,
    isRepliesLoading: Boolean = false,
    hideReplyOption: Boolean = false,
    modifier: Modifier = Modifier
) {
    var isRepliesExpanded by remember { mutableStateOf(false) }
    
    // Debug log to verify comment data and like state
    Log.d("CommentItemCard", "Rendering comment id=${comment.id}, liked=${comment.liked}, likeCount=${comment.likeCount}")

    Card(
        modifier = modifier
            .fillMaxWidth(),
        elevation = CardDefaults.cardElevation(defaultElevation = 2.dp),
        colors = CardDefaults.cardColors(
            containerColor = ImdbDarkGray,  // COMMENTS CARD BACKGROUND
            contentColor = ImdbWhite        // IMDB uses white text on dark backgrounds
        ),
        shape = RoundedCornerShape(0.dp)    // IMDB typically uses very subtle or no rounded corners
    ) {
        Column(
            modifier = Modifier
                .padding(24.dp)
                .fillMaxWidth()
        ) {
            // Comment header with username and date
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.SpaceBetween
            ) {
                Text(
                    text = comment.username,
                    style = MaterialTheme.typography.titleMedium,
                    color = ImdbWhite
                )
                Text(
                    text = formatDate(comment.createdAt),
                    style = MaterialTheme.typography.bodySmall,
                    color = ImdbLightGray
                )
            }

            Spacer(modifier = Modifier.height(16.dp))

            // Comment body text
            Text(
                text = comment.text,
                style = MaterialTheme.typography.bodyMedium,
                modifier = Modifier.fillMaxWidth(),
                color = ImdbWhite
            )

            Spacer(modifier = Modifier.height(16.dp))

            // Like, Reply and Replies section
            Row(
                modifier = Modifier.fillMaxWidth(),
                verticalAlignment = Alignment.CenterVertically
            ) {
                // Always show the like button - even if the callback is null
                // This ensures the UI element is visible, but just won't respond to clicks if no callback
                Row(
                    modifier = Modifier
                        .clickable { 
                            Log.d("CommentItemCard", "Like button clicked, callback is ${if (onLike == null) "NULL" else "available"}")
                            onLike?.invoke(comment) 
                        }
                        .padding(vertical = 8.dp, horizontal = 8.dp),
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        imageVector = if (comment.liked) Icons.Filled.ThumbUp else Icons.Outlined.ThumbUp,
                        contentDescription = if (comment.liked) "Unlike comment" else "Like comment",
                        tint = if (comment.liked) ImdbYellow else ImdbLightGray,  // IMDB uses yellow for selected elements
                        modifier = Modifier.size(18.dp)
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Text(
                        text = if (comment.likeCount == 1) "1 like" else "${comment.likeCount} likes",
                        style = MaterialTheme.typography.bodySmall,
                        color = ImdbLightGray
                    )
                }
                
                Spacer(modifier = Modifier.width(16.dp))

                // Reply text (clickable) - only show if not hidden
                if (!hideReplyOption) {
                    Text(
                        text = "Reply",
                        style = MaterialTheme.typography.bodyMedium,
                        color = ImdbYellow,  // IMDB uses yellow for interactive elements
                        modifier = Modifier
                            .clickable { onReply(comment) }
                            .padding(vertical = 8.dp, horizontal = 8.dp)
                    )

                    Spacer(modifier = Modifier.width(16.dp))
                }

                // Replies section (only show if there are replies and onViewReplies is provided)
                if (repliesCount > 0 && onViewReplies != null) {
                    Row(
                        modifier = Modifier
                            .clickable {
                                if (!isRepliesLoading) {
                                    if (!isRepliesExpanded) {
                                        onViewReplies(comment)
                                    }
                                    isRepliesExpanded = !isRepliesExpanded
                                }
                            }
                            .padding(8.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        if (isRepliesLoading) {
                            CircularProgressIndicator(
                                modifier = Modifier.size(16.dp),
                                strokeWidth = 2.dp,
                                color = ImdbYellow  // IMDB uses yellow for loading indicators
                            )
                            Spacer(modifier = Modifier.width(8.dp))
                            Text(
                                text = "Loading replies...",
                                style = MaterialTheme.typography.bodySmall,
                                color = ImdbLightGray
                            )
                        } else {
                            Text(
                                text = "$repliesCount ${if (repliesCount == 1) "reply" else "replies"}",
                                style = MaterialTheme.typography.bodyMedium,
                                color = ImdbYellow,  // IMDB uses yellow for interactive elements
                                maxLines = 1,
                                overflow = TextOverflow.Ellipsis
                            )
                            Spacer(modifier = Modifier.width(4.dp))
                            Icon(
                                imageVector = if (isRepliesExpanded)
                                    Icons.Default.KeyboardArrowDown
                                else
                                    Icons.Default.KeyboardArrowRight,
                                contentDescription = if (isRepliesExpanded)
                                    "Hide replies"
                                else
                                    "View replies",
                                modifier = Modifier.size(20.dp),
                                tint = ImdbYellow  // IMDB uses yellow for interactive icons
                            )
                        }
                    }
                }
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
fun CommentItemCardBasicPreview() {
    ThyViewTheme {
        CommentItemCard(
        comment = Comment(
            id = "1",
            externalAuthorId = "user123",
            username = "JohnDoe",
            text = "This is a sample comment that shows how the card looks.",
            createdAt = "2023-10-15T14:30:00Z",
            likeCount = 5,
            liked = true
        ),
        repliesCount = 2,
        onReply = {},
        onLike = {},
        onViewReplies = {}
        )
    }
}

@Preview(showBackground = true)
@Composable
fun CommentItemCardHiddenReplyPreview() {
    ThyViewTheme {
        CommentItemCard(
        comment = Comment(
            id = "1",
            externalAuthorId = "user123",
            username = "JohnDoe",
            text = "This preview shows how the card looks with hidden reply option.",
            createdAt = "2023-10-15T14:30:00Z",
            likeCount = 0,
            liked = false
        ),
        repliesCount = 2,
        onReply = {},
        onLike = {},
        onViewReplies = {},
        hideReplyOption = true
        )
    }
}

@Preview(showBackground = true)
@Composable
fun CommentItemCardLongTextPreview() {
    ThyViewTheme {
        CommentItemCard(
        comment = Comment(
            id = "2",
            externalAuthorId = "user456",
            username = "JaneSmith",
            text = """
                This is a much longer sample comment that demonstrates how the card 
                handles multi-line text content. It should wrap properly and maintain the 
                correct layout of all other elements.
                
                We might have multiple paragraphs to show as well, which helps us verify 
                the spacing and overall appearance of the comment card with substantial
                content that might appear in real-world scenarios. This helps us evaluate 
                and test the layout capabilities of our CommentItemCard component.
            """.trimIndent(),
            createdAt = "2023-10-10T09:15:00Z",
            likeCount = 12,
            liked = false
        ),
        repliesCount = 5,
        onReply = {},
        onLike = {},
        onViewReplies = {}
        )
    }
}

@Preview(showBackground = true)
@Composable
fun CommentItemCardLoadingRepliesPreview() {
    ThyViewTheme {
        CommentItemCard(
        comment = Comment(
            id = "3",
            externalAuthorId = "user789",
            username = "AlexJohnson",
            text = "This preview shows how the card looks when replies are loading.",
            createdAt = "2023-10-14T11:22:00Z",
            likeCount = 3,
            liked = true
        ),
        repliesCount = 3,
        onReply = {},
        onLike = {},
        onViewReplies = {},
        isRepliesLoading = true
        )
    }
}

class CommentProvider : PreviewParameterProvider<Comment> {
    override val values = sequenceOf(
        Comment(
            id = "4",
            externalAuthorId = "user101",
            username = "SamuelWilson",
            text = "Parameterized preview with various comment states.",
            createdAt = "2023-10-01T16:45:00Z",
            replyCount = 12,
            likeCount = 7,
            liked = true
        )
    )
}

@Preview(showBackground = true)
@Composable
fun CommentItemCardParameterizedPreview(
    @PreviewParameter(CommentProvider::class) comment: Comment
) {
    ThyViewTheme {
        CommentItemCard(
        comment = comment,
        repliesCount = comment.replyCount,
        onReply = {},
        onLike = {},
        onViewReplies = {}
        )
    }
}

// Additional preview that explicitly demonstrates both liked and unliked states
@Preview(showBackground = true)
@Composable
fun CommentItemCardLikePreview() {
    ThyViewTheme {
        Column {
            // Liked comment
            CommentItemCard(
                comment = Comment(
                    id = "like1",
                    externalAuthorId = "userLike",
                    username = "LikedComment",
                    text = "This comment is liked (liked=true)",
                    createdAt = "2023-11-15T12:30:00Z",
                    likeCount = 42,
                    liked = true
                ),
                repliesCount = 0,
                onReply = {},
                onLike = {},
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // Unliked comment
            CommentItemCard(
                comment = Comment(
                    id = "like2",
                    externalAuthorId = "userUnlike",
                    username = "UnlikedComment",
                    text = "This comment is not liked (liked=false)",
                    createdAt = "2023-11-15T12:31:00Z",
                    likeCount = 7,
                    liked = false
                ),
                repliesCount = 0,
                onReply = {},
                onLike = {},
            )
            
            Spacer(modifier = Modifier.height(16.dp))
            
            // No callback provided
            CommentItemCard(
                comment = Comment(
                    id = "like3",
                    externalAuthorId = "userNoCallback",
                    username = "NoCallbackComment",
                    text = "This comment has no like callback (onLike=null)",
                    createdAt = "2023-11-15T12:32:00Z",
                    likeCount = 3,
                    liked = true
                ),
                repliesCount = 0,
                onReply = {},
                onLike = null,
            )
        }
    }
}
