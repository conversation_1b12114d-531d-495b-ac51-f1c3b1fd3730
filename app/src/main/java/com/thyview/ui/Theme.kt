package com.thyview.ui

import androidx.compose.foundation.isSystemInDarkTheme
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.darkColorScheme
import androidx.compose.material3.lightColorScheme
import androidx.compose.runtime.Composable
import androidx.compose.ui.graphics.Color

// IMDB-style colors
// Main brand yellow color of IMDB
val ImdbYellow = Color(0xFFF5C518)
// Dark background colors
val ImdbBlack = Color(0xFF000000)
val ImdbDarkGray = Color(0xFF121212)
val ImdbMediumGray = Color(0xFF242424)
val ImdbLightGray = Color(0xFF8E8E8E)
// Text colors
val ImdbWhite = Color(0xFFFFFFFF)
val ImdbOffWhite = Color(0xFFE3E3E3)

@Composable
fun ThyViewTheme(
    darkTheme: Boolean = isSystemInDarkTheme(),
    content: @Composable () -> Unit
) {
    // IMDB primarily uses a dark theme, so we'll make both themes similar
    // with slight adjustments for better readability in light mode
    
    // Dark theme colors (IMDB default style)
    val darkColorScheme = darkColorScheme(
        primary = ImdbYellow,            // Primary brand color (yellow)
        secondary = ImdbYellow,          // Secondary color (also yellow for consistency)
        tertiary = ImdbYellow,           // Tertiary color (also yellow for IMDB-like appearance)
        background = ImdbBlack,          // Main background
        surface = ImdbDarkGray,          // Card and surface backgrounds
        surfaceVariant = ImdbMediumGray, // Alternative surface color for layering
        onPrimary = ImdbBlack,           // Text/icons on primary color
        onSecondary = ImdbBlack,         // Text/icons on secondary color  
        onTertiary = ImdbBlack,          // Text/icons on tertiary color
        onBackground = ImdbWhite,        // Text/icons on background
        onSurface = ImdbWhite,           // Text/icons on surface
        onSurfaceVariant = ImdbOffWhite  // Secondary text on surfaces
    )

    // Light theme - slightly modified for better readability but still IMDB-inspired
    val lightColorScheme = lightColorScheme(
        primary = ImdbYellow,            // Primary brand color (yellow)
        secondary = ImdbYellow,          // Secondary color (also yellow)
        tertiary = ImdbYellow,           // Tertiary accent
        background = Color(0xFF1A1A1A),  // Slightly lighter than pure black for light mode
        surface = ImdbDarkGray,          // Card backgrounds
        surfaceVariant = ImdbMediumGray, // Alternative surface color
        onPrimary = ImdbBlack,           // Text/icons on primary color
        onSecondary = ImdbBlack,         // Text/icons on secondary color
        onTertiary = ImdbBlack,          // Text/icons on tertiary color
        onBackground = ImdbWhite,        // Text/icons on background
        onSurface = ImdbWhite,           // Text/icons on surface
        onSurfaceVariant = ImdbOffWhite  // Secondary text on surfaces
    )

    val colorScheme = if (darkTheme) {
        darkColorScheme
    } else {
        lightColorScheme
    }

    MaterialTheme(
        colorScheme = colorScheme,
        typography = Typography,
        content = content
    )
}
