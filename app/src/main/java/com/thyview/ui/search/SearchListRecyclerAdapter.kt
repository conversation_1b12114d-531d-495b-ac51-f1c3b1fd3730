package com.thyview.ui.search

import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.recyclerview.widget.RecyclerView
import com.bumptech.glide.request.RequestOptions
import com.thyview.R
import com.thyview.databinding.SearchCinemaEntityItemBinding
import com.thyview.glide.GlideApp
import com.thyview.models.CinemaEntity

class SearchListRecyclerAdapter(private var favorites: ArrayList<CinemaEntity>, val context: Context) : RecyclerView.Adapter<SearchListRecyclerAdapter.SearchItemViewHolder>() {

    private lateinit var listener: OnItemClickListener

    override fun onBindViewHolder(holder: SearchItemViewHolder, position: Int) {
        holder.name.text = favorites[position].name
        GlideApp.with(context)
            .load(favorites[position].imageUrl)
            .placeholder(R.drawable.profile_user)
            .error(R.drawable.profile_user)
            .apply(RequestOptions.circleCropTransform())
            .into(holder.image)

        holder.itemView.setOnClickListener {
            listener.onClick(it, favorites[position])
        }
    }
    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): SearchItemViewHolder {
        val itemBinding = SearchCinemaEntityItemBinding.inflate(LayoutInflater.from(parent.context), parent, false)
        setOnItemClickListener(listener)
        return SearchItemViewHolder(itemBinding)
    }

    override fun getItemCount(): Int {
        return favorites.size
    }

    interface OnItemClickListener {
        fun onClick(view: View, data: CinemaEntity)
    }

    fun setOnItemClickListener(listener: OnItemClickListener) {
        this.listener = listener
    }

    fun filterList(filterlist: ArrayList<CinemaEntity>) {
        favorites = filterlist
        notifyDataSetChanged()
    }

    class SearchItemViewHolder(itemView: SearchCinemaEntityItemBinding) : RecyclerView.ViewHolder(itemView.root) {
        val name: TextView = itemView.itemName
        val image: ImageView = itemView.itemImageView
    }
}