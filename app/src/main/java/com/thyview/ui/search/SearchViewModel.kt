package com.thyview.ui.search

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.thyview.models.search.SearchResult
import com.thyview.services.SearchService
import com.thyview.utils.SearchHistoryManager
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.FlowPreview
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

/**
 * ViewModel for search functionality with debounced search and history management
 */
@OptIn(FlowPreview::class)
@HiltViewModel
class SearchViewModel @Inject constructor(
    private val searchService: SearchService,
    private val searchHistoryManager: SearchHistoryManager
) : ViewModel() {

    private val _uiState = MutableStateFlow(SearchUiState())
    val uiState: StateFlow<SearchUiState> = _uiState.asStateFlow()

    private val _searchQuery = MutableStateFlow("")

    init {
        // Load search history on initialization
        loadSearchHistory()

        // Set up debounced search
        _searchQuery
            .debounce(500) // Wait 500ms after user stops typing
            .filter { it.length >= 3 } // Only search if 3+ characters
            .distinctUntilChanged()
            .onEach { query ->
                performSearchInternal(query)
            }
            .launchIn(viewModelScope)
    }

    /**
     * Update search query and trigger debounced search
     */
    fun updateQuery(query: String) {
        _uiState.update { it.copy(query = query) }
        _searchQuery.value = query

        // Clear results if query is too short
        if (query.length < 3) {
            _uiState.update { it.copy(searchResults = emptyList(), error = null) }
        }
    }

    /**
     * Perform immediate search (called when user presses search button)
     */
    fun performSearch(query: String) {
        if (query.length >= 3) {
            performSearchInternal(query)
        }
    }

    /**
     * Select item from search history
     */
    fun selectHistoryItem(query: String) {
        updateQuery(query)
        performSearchInternal(query)
    }

    /**
     * Clear search history
     */
    fun clearSearchHistory() {
        searchHistoryManager.clearSearchHistory()
        _uiState.update { it.copy(searchHistory = emptyList()) }
    }

    /**
     * Load search history from storage
     */
    private fun loadSearchHistory() {
        val history = searchHistoryManager.getSearchHistory()
        _uiState.update { it.copy(searchHistory = history) }
    }

    /**
     * Internal search implementation
     */
    private fun performSearchInternal(query: String) {
        if (query.isBlank() || query.length < 3) return

        viewModelScope.launch {
            _uiState.update { it.copy(isLoading = true, error = null) }

            try {
                val result = searchService.search(query)

                result.fold(
                    onSuccess = { searchResponse ->
                        // Add to search history
                        searchHistoryManager.addSearchQuery(query)
                        loadSearchHistory()

                        _uiState.update {
                            it.copy(
                                searchResults = searchResponse.results,
                                isLoading = false,
                                error = null
                            )
                        }

                        Timber.d("Search completed successfully for: $query, found ${searchResponse.results.size} results")
                    },
                    onFailure = { exception ->
                        _uiState.update {
                            it.copy(
                                searchResults = emptyList(),
                                isLoading = false,
                                error = exception.message ?: "Search failed"
                            )
                        }

                        Timber.e(exception, "Search failed for: $query")
                    }
                )
            } catch (e: Exception) {
                _uiState.update {
                    it.copy(
                        searchResults = emptyList(),
                        isLoading = false,
                        error = e.message ?: "Unexpected error occurred"
                    )
                }

                Timber.e(e, "Unexpected error during search for: $query")
            }
        }
    }
}

/**
 * UI state for search screen
 */
data class SearchUiState(
    val query: String = "",
    val searchResults: List<SearchResult> = emptyList(),
    val searchHistory: List<String> = emptyList(),
    val isLoading: Boolean = false,
    val error: String? = null
)