package com.thyview.ui.components

import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.padding
import androidx.compose.material.AlertDialog
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Text
import androidx.compose.material.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.runtime.MutableState
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp
import androidx.compose.ui.window.DialogProperties

/**
 * A reusable Composable Alert Dialog component that provides a consistent UI for displaying alerts
 * across the application. It replaces the traditional AlertDialogView for Compose screens.
 */
@Composable
fun ComposeAlertDialog(
    showDialog: MutableState<Boolean>,
    title: String,
    message: String,
    positiveButtonText: String,
    negativeButtonText: String? = null,
    onPositiveClick: () -> Unit,
    onNegativeClick: (() -> Unit)? = null,
    onDismiss: (() -> Unit)? = null,
    isCancelable: Boolean = true
) {
    if (showDialog.value) {
        AlertDialog(
            onDismissRequest = {
                if (isCancelable) {
                    showDialog.value = false
                    onDismiss?.invoke()
                }
            },
            title = {
                Text(
                    text = title,
                    style = MaterialTheme.typography.h6,
                    textAlign = TextAlign.Start
                )
            },
            text = {
                Text(
                    text = message,
                    style = MaterialTheme.typography.body1,
                    modifier = Modifier.fillMaxWidth()
                )
            },
            confirmButton = {
                TextButton(
                    onClick = {
                        showDialog.value = false
                        onPositiveClick()
                    },
                    modifier = Modifier.padding(horizontal = 8.dp)
                ) {
                    Text(positiveButtonText)
                }
            },
            dismissButton = negativeButtonText?.let {
                {
                    TextButton(
                        onClick = {
                            showDialog.value = false
                            onNegativeClick?.invoke()
                        },
                        modifier = Modifier.padding(horizontal = 8.dp)
                    ) {
                        Text(negativeButtonText)
                    }
                }
            },
            properties = DialogProperties(
                dismissOnBackPress = isCancelable,
                dismissOnClickOutside = isCancelable
            )
        )
    }
}

/**
 * Extension function to create and show an alert dialog in a more concise manner.
 * Example usage:
 * ```
 * val showDialog = remember { mutableStateOf(false) }
 * showDialog.showAlert(
 *     title = "Error",
 *     message = "Something went wrong",
 *     positiveButtonText = "OK"
 * ) {
 *     // Handle positive button click
 * }
 * ```
 */
@Composable
fun MutableState<Boolean>.showAlert(
    title: String,
    message: String,
    positiveButtonText: String,
    negativeButtonText: String? = null,
    onPositiveClick: () -> Unit = {},
    onNegativeClick: (() -> Unit)? = null
) {
    this.value = true
    ComposeAlertDialog(
        showDialog = this,
        title = title,
        message = message,
        positiveButtonText = positiveButtonText,
        negativeButtonText = negativeButtonText,
        onPositiveClick = onPositiveClick,
        onNegativeClick = onNegativeClick
    )
}