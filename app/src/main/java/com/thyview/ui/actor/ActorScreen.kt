package com.thyview.ui.actor

import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.MaterialTheme
import androidx.compose.material3.Scaffold
import androidx.compose.material3.Text
import androidx.compose.material3.TopAppBar
import androidx.compose.material3.TopAppBarDefaults
import androidx.compose.runtime.Composable
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.navigation.NavController
import androidx.navigation.compose.rememberNavController
import com.thyview.ui.ImdbBlack
import com.thyview.ui.ImdbYellow
import com.thyview.ui.ThyViewTheme

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ActorScreen(
    navController: NavController,
    actorId: String? = null,
    viewModel: ActorViewModel = viewModel()
) {
    ThyViewTheme {
        Scaffold(
            topBar = {
                TopAppBar(
                    title = { 
                        Text(
                            text = "Actor Details",
                            color = ImdbYellow
                        ) 
                    },
                    colors = TopAppBarDefaults.topAppBarColors(
                        containerColor = ImdbBlack,
                        titleContentColor = ImdbYellow
                    )
                )
            }
    ) { paddingValues ->
        Column(
            modifier = Modifier
                .fillMaxSize()
                .padding(paddingValues)
                .padding(16.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Top
        ) {
            // Actor content
            Text(
                text = "Actor Details",
                style = MaterialTheme.typography.headlineMedium,
                textAlign = TextAlign.Center,
                color = MaterialTheme.colorScheme.onBackground
            )
            
            Text(
                text = actorId?.let { "Actor ID: $it" } ?: "No actor selected",
                style = MaterialTheme.typography.bodyLarge,
                modifier = Modifier.padding(top = 8.dp),
                color = MaterialTheme.colorScheme.onBackground
            )
            
            // TODO: Add actor details UI
            Text(
                text = "Actor details will be shown here",
                style = MaterialTheme.typography.bodyMedium,
                modifier = Modifier.padding(top = 16.dp),
                color = MaterialTheme.colorScheme.onSurfaceVariant
            )
        }
    }
}
}

@Preview(showBackground = true, widthDp = 360)
@Composable
fun ActorScreenPreview() {
    // Using a NavController for the preview
    val navController = rememberNavController()
    
    // Preview with sample data
    ActorScreen(
        navController = navController,
        actorId = "sample-actor-123"
    )
}
