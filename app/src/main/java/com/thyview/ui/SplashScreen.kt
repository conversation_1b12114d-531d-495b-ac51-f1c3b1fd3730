
package com.thyview.ui

import android.app.Activity
import android.content.Context
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.padding
import androidx.compose.material.MaterialTheme
import androidx.compose.material.Surface
import androidx.compose.material.Text
import androidx.compose.foundation.Image
import androidx.compose.material3.Surface
import com.thyview.R
import androidx.compose.ui.res.painterResource
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewmodel.compose.viewModel
import androidx.navigation.NavController
import androidx.navigation.compose.rememberNavController
import com.thyview.analytics.MP_SCREEN_NAME
import com.thyview.analytics.MP_SCREEN_VIEWED
import com.thyview.analytics.MixPanelAnalyticsTrackingService
import com.thyview.firebase.config.RemoteConfigurationService
import com.thyview.services.AuthService
import com.thyview.utils.AppUpdateHelperUtility
import com.thyview.utils.ScreenRouter
import kotlinx.coroutines.delay
import timber.log.Timber

/**
 * ViewModel to manage splash screen state and operations
 */
class SplashViewModel : ViewModel() {
    var forceUpdateRequired by mutableStateOf(false)
    var isAppRestricted by mutableStateOf(false)
    var isLoading by mutableStateOf(true)
    var navigateToReviews by mutableStateOf(false)

    fun checkForUpdates(context: Context) {
        val appUpdateHelperUtility = AppUpdateHelperUtility(context as Activity)

        RemoteConfigurationService.fetchConfig(RemoteConfigurationService.APP_FORCE_UPDATE_VERSION) { forceUpgradeVersion ->
            forceUpdateRequired = appUpdateHelperUtility.isImmediateUpdateRequired(
                forceUpgradeVersion,
                context.applicationContext as android.app.Application
            )

            if (!forceUpdateRequired) {
                checkIfAppIsRestricted(context)
            }
        }
    }

    fun checkIfAppIsRestricted(context: Context) {
        RemoteConfigurationService.fetchConfig(RemoteConfigurationService.IS_APP_LOCATION_RESTRICTED) { isRestricted ->
            isAppRestricted = isRestricted.toBoolean()

            if (!isAppRestricted) {
                checkLastSeenScreen(context)
            }
        }
    }

    fun checkLastSeenScreen(context: Context) {
        val screen = ScreenRouter.getLastScreenScreen(context)
        navigateToReviews = true
        isLoading = false
    }

    fun resetForRestart() {
        isLoading = true
        forceUpdateRequired = false
        isAppRestricted = false
        navigateToReviews = false
    }
}

/**
 * Main splash screen composable
 */
@Composable
fun SplashScreen(
    navController: NavController,
    viewModel: SplashViewModel = viewModel()
) {
    val context = LocalContext.current
    val splashDisplayLength: Long = 500 // milliseconds

    // Track screen view
    LaunchedEffect(Unit) {
        val map = hashMapOf<String, String>()
        map[MP_SCREEN_NAME] = "Splash"
        MixPanelAnalyticsTrackingService.logEvent(context, MP_SCREEN_VIEWED, map)
    }

    // Show splash screen with delay
    LaunchedEffect(Unit) {
        delay(splashDisplayLength)
        viewModel.checkForUpdates(context)
    }

    // Navigation effects
    LaunchedEffect(viewModel.forceUpdateRequired) {
        if (viewModel.forceUpdateRequired) {
            val appUpdateHelperUtility = AppUpdateHelperUtility(context as Activity)
            appUpdateHelperUtility.startImmediateUpdate()
        }
    }

    LaunchedEffect(viewModel.isAppRestricted) {
        if (viewModel.isAppRestricted) {
            // Existing logic for restriction can remain here
        }
    }

    LaunchedEffect(viewModel.navigateToReviews) {
        if (viewModel.navigateToReviews) {
            navController.navigate("reviews") {
                popUpTo(navController.graph.startDestinationId) {
                    inclusive = true
                }
            }
        }
    }


    ThyViewTheme {
        Surface(
            modifier = Modifier.fillMaxSize(),
            color = androidx.compose.material3.MaterialTheme.colorScheme.background
        ) {
            SplashContent()
        }
    }
}


@Composable
fun SplashContent() {
    Column(
        modifier = Modifier.fillMaxSize(),
        verticalArrangement = Arrangement.Center,
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = stringResource(id = R.string.app_name).uppercase(),
            style = MaterialTheme.typography.h4,
            fontWeight = FontWeight.Bold,
            color = ImdbYellow
        )
    }
}

/**
 * Preview for the SplashContent
 * This preview is simpler as it doesn't require any dependencies like NavController or ViewModel
 */
@Preview(name = "Splash Content Preview", showBackground = true)
@Composable
fun SplashContentPreview() {
    ThyViewTheme {
        SplashContent()
    }
}

/**
 * Preview for the full SplashScreen with mocked dependencies
 */
@Preview(name = "Splash Screen Preview", showBackground = true)
@Composable
fun SplashScreenPreview() {
    val navController = rememberNavController()
    // Create a mock view model for the preview
    val mockViewModel = SplashViewModel().apply {
        // Set the state for preview purposes
        isLoading = false
    }
    
    ThyViewTheme {
        SplashScreen(
            navController = navController,
            viewModel = mockViewModel
        )
    }
}
