package com.thyview.ui.details

import android.content.Intent
import android.net.Uri
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.automirrored.filled.ArrowForward
import androidx.compose.material.icons.automirrored.filled.ArrowRight
import androidx.compose.material.icons.automirrored.outlined.ArrowRight
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.ArrowForward
import androidx.compose.material.icons.filled.ChevronRight
import androidx.compose.material.icons.filled.Favorite
import androidx.compose.material.icons.filled.FavoriteBorder
import androidx.compose.material.icons.filled.PlayArrow
import androidx.compose.material.icons.filled.Star
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.tooling.preview.Preview
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavController
import coil.compose.AsyncImage
import com.thyview.R
import com.thyview.models.search.DetailedCastMember
import com.thyview.models.search.MovieDetails
import com.thyview.models.search.StreamingService
import com.thyview.models.search.Genre
import com.thyview.models.search.SpokenLanguage
import com.thyview.models.search.ProductionCompany
import com.thyview.models.search.ProductionCountry
import com.thyview.models.search.CrewMember
import com.thyview.models.search.PersonSummary
import com.thyview.models.search.SimilarItem
import com.thyview.models.search.Collection
import com.thyview.ui.ThyViewTheme
import com.thyview.ui.ImdbBlack
import com.thyview.ui.ImdbYellow
import com.thyview.ui.ImdbWhite
import com.thyview.ui.ImdbDarkGray
import com.thyview.ui.ImdbMediumGray
import com.thyview.ui.NavRoutes

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun MovieDetailsScreen(
    movieId: Int,
    navController: NavController,
    viewModel: MovieDetailsViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsState()
    val context = LocalContext.current

    LaunchedEffect(movieId) {
        viewModel.loadMovieDetails(movieId)
    }

    ThyViewTheme {
        Scaffold(
            topBar = {
                TopAppBar(
                    title = { 
                        Text(
                            text = uiState.movieDetails?.title ?: "Movie Details",
                            color = ImdbYellow,
                            maxLines = 1,
                            overflow = TextOverflow.Ellipsis
                        ) 
                    },
                    navigationIcon = {
                        IconButton(onClick = { navController.navigateUp() }) {
                            Icon(
                                imageVector = Icons.Default.ArrowBack,
                                contentDescription = "Back",
                                tint = ImdbYellow
                            )
                        }
                    },
                    colors = TopAppBarDefaults.topAppBarColors(
                        containerColor = ImdbBlack,
                        titleContentColor = ImdbYellow
                    )
                )
            },
            containerColor = ImdbBlack
        ) { innerPadding ->
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(innerPadding)
                    .background(ImdbBlack)
            ) {
                when {
                    uiState.isLoading -> {
                        CircularProgressIndicator(
                            modifier = Modifier.align(Alignment.Center),
                            color = ImdbYellow
                        )
                    }
                    
                    uiState.error != null -> {
                        Column(
                            modifier = Modifier.align(Alignment.Center),
                            horizontalAlignment = Alignment.CenterHorizontally
                        ) {
                            Text(
                                text = "Technical error, try again later",
                                color = ImdbWhite,
                                fontSize = 16.sp
                            )
                            Spacer(modifier = Modifier.height(8.dp))
                            Text(
                                text = uiState.error ?: "Unknown error",
                                color = ImdbWhite.copy(alpha = 0.7f),
                                fontSize = 12.sp
                            )
                        }
                    }
                    
                    uiState.movieDetails != null -> {
                        val movieDetails = uiState.movieDetails
                        MovieDetailsContent(
                            movieDetails = movieDetails!!,
                            isInWatchlist = uiState.isInWatchlist,
                            streamingServices = uiState.streamingServices,
                            onWatchlistToggle = { viewModel.toggleWatchlist() },
                            onCastMemberClick = { castMember ->
                                navController.navigate("person_details/${castMember.id}")
                            },
                            onSeeAllReviewsClick = {
                                // Navigate to PostsScreen with movie parameter
                                navController.navigate("reviews?movie=${movieDetails?.title}")
                            },
                            onRatingClick = {
                                // Navigate to AddReviewScreen for rating
                                navController.navigate(NavRoutes.addReview(movieDetails?.title))
                            },
                            onTrailerClick = {
                                // Open YouTube with hardcoded link
                                val intent = Intent(Intent.ACTION_VIEW, Uri.parse("https://www.youtube.com/watch?v=dQw4w9WgXcQ"))
                                context.startActivity(intent)
                            },
                            onWhereToWatchClick = {
                                navController.navigate("streaming_services/${movieId}/movie")
                            }
                        )
                    }
                }
            }
        }
    }
}

@Composable
fun MovieDetailsContent(
    movieDetails: MovieDetails,
    isInWatchlist: Boolean,
    streamingServices: List<StreamingService>,
    onWatchlistToggle: () -> Unit,
    onCastMemberClick: (DetailedCastMember) -> Unit,
    onSeeAllReviewsClick: () -> Unit,
    onRatingClick: () -> Unit,
    onTrailerClick: () -> Unit,
    onWhereToWatchClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    LazyColumn(
        modifier = modifier.fillMaxSize(),
        contentPadding = PaddingValues(0.dp),
        verticalArrangement = Arrangement.spacedBy(0.dp)
    ) {
        item {
            // Hero section with backdrop and overlay info
            Box(
                modifier = Modifier
                    .fillMaxWidth()
                    .height(450.dp)
            ) {
                // Backdrop image
                AsyncImage(
                    model = movieDetails.backdrop,
                    contentDescription = null,
                    modifier = Modifier.fillMaxSize(),
                    contentScale = ContentScale.Crop
                )

                // Dark gradient overlay - adjusted for better backdrop visibility
                Box(
                    modifier = Modifier
                        .fillMaxSize()
                        .background(
                            Brush.verticalGradient(
                                colors = listOf(
                                    Color.Transparent,
                                    Color.Transparent,
                                    ImdbBlack.copy(alpha = 0.3f),
                                    ImdbBlack.copy(alpha = 0.7f),
                                    ImdbBlack
                                ),
                                startY = 0f,
                                endY = Float.POSITIVE_INFINITY
                            )
                        )
                )

                // Content overlay - positioned higher for better backdrop visibility
                Row(
                    modifier = Modifier
                        .fillMaxWidth()
                        .align(Alignment.BottomStart)
                        .padding(start = 16.dp, end = 16.dp, bottom = 24.dp, top = 16.dp),
                    horizontalArrangement = Arrangement.spacedBy(16.dp),
                    verticalAlignment = Alignment.Bottom
                ) {
                    // Movie info
                    Column(
                        modifier = Modifier.weight(1f),
                        verticalArrangement = Arrangement.spacedBy(8.dp)
                    ) {
                        Text(
                            text = movieDetails.title,
                            color = ImdbWhite,
                            fontSize = 24.sp,
                            fontWeight = FontWeight.Bold
                        )

                        Text(
                            text = "${movieDetails.year} • DIRECTED BY",
                            color = ImdbWhite.copy(alpha = 0.8f),
                            fontSize = 14.sp
                        )

                        Text(
                            text = movieDetails.director,
                            color = ImdbWhite,
                            fontSize = 14.sp,
                            fontWeight = FontWeight.Medium
                        )

                        // Trailer button with runtime aligned to the right
                        Row(
                            modifier = Modifier.fillMaxWidth(),
                            horizontalArrangement = Arrangement.spacedBy(8.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Button(
                                onClick = onTrailerClick,
                                colors = ButtonDefaults.buttonColors(containerColor = ImdbYellow),
                                modifier = Modifier.height(36.dp)
                            ) {
                                Icon(
                                    imageVector = Icons.Default.PlayArrow,
                                    contentDescription = null,
                                    tint = ImdbBlack,
                                    modifier = Modifier.size(16.dp)
                                )
                                Spacer(modifier = Modifier.width(4.dp))
                                Text(
                                    text = "Thyview Review",
                                    color = ImdbBlack,
                                    fontSize = 12.sp
                                )
                            }

                            Spacer(modifier = Modifier.weight(1f))

                            Text(
                                text = "${movieDetails.runtime} mins",
                                color = ImdbWhite.copy(alpha = 0.7f),
                                fontSize = 12.sp
                            )
                        }
                    }

                    // Movie poster - positioned on the right side, increased height for better prominence
                    AsyncImage(
                        model = movieDetails.poster,
                        contentDescription = null,
                        modifier = Modifier
                            .width(85.dp)
                            .height(150.dp)
                            .clip(RoundedCornerShape(8.dp))
                            .background(ImdbMediumGray),
                        contentScale = ContentScale.Crop
                    )
                }
            }
        }

        item {
            // Description section
            var isExpanded by remember { mutableStateOf(false) }

            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp)
                    .padding(top = 16.dp)
            ) {
                Text(
                    text = movieDetails.overview,
                    color = ImdbWhite.copy(alpha = 0.9f),
                    fontSize = 14.sp,
                    maxLines = if (isExpanded) Int.MAX_VALUE else 5,
                    overflow = TextOverflow.Ellipsis
                )
                if (movieDetails.overview.length > 200) {
                    Spacer(modifier = Modifier.height(8.dp))
                    TextButton(
                        onClick = { isExpanded = !isExpanded },
                        contentPadding = PaddingValues(0.dp)
                    ) {
                        Text(
                            text = if (isExpanded) "Show less" else "Show more",
                            color = ImdbWhite,
                            fontSize = 14.sp
                        )
                    }
                }
            }
        }

        item {
            // Rating section
            Row(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp)
                    .padding(top = 16.dp),
                horizontalArrangement = Arrangement.SpaceBetween,
                verticalAlignment = Alignment.CenterVertically
            ) {
                // Star rating
                Column(
                    horizontalAlignment = Alignment.CenterHorizontally,
                    modifier = Modifier.weight(1f)
                ) {
                    Row(
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Icon(
                            imageVector = Icons.Default.Star,
                            contentDescription = null,
                            tint = ImdbYellow,
                            modifier = Modifier.size(24.dp)
                        )
                        Spacer(modifier = Modifier.width(8.dp))
                        Text(
                            text = String.format("%.1f", movieDetails.rating),
                            color = ImdbWhite,
                            fontSize = 20.sp,
                            fontWeight = FontWeight.Bold
                        )
                        Text(
                            text = "/10",
                            color = ImdbWhite.copy(alpha = 0.7f),
                            fontSize = 16.sp
                        )
                    }
                    Text(
                        text = "${movieDetails.voteCount.toString().replace(Regex("(\\d)(?=(\\d{3})+$)"), "$1,")}",
                        color = ImdbWhite.copy(alpha = 0.7f),
                        fontSize = 12.sp
                    )
                }

                // Rate this button
                Box(
                    modifier = Modifier.weight(1f),
                    contentAlignment = Alignment.Center
                ) {
                    Column(
                        horizontalAlignment = Alignment.CenterHorizontally,
                        modifier = Modifier.clickable { onRatingClick() }
                    ) {
                        Icon(
                            imageVector = Icons.Default.Star,
                            contentDescription = null,
                            tint = ImdbWhite,
                            modifier = Modifier.size(20.dp)
                        )
                        Spacer(modifier = Modifier.height(4.dp))
                        Text(
                            text = "Rate this",
                            color = ImdbWhite,
                            fontSize = 14.sp,
                            fontWeight = FontWeight.Medium
                        )
                    }
                }
            }
        }

        item {
            // See all user reviews section
            TextButton(
                onClick = onSeeAllReviewsClick,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp)
                    .padding(top = 16.dp),
                contentPadding = PaddingValues(0.dp)
            ) {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "See all user reviews",
                        color = ImdbWhite,
                        fontSize = 16.sp
                    )
                    Icon(
                        imageVector = Icons.Filled.ChevronRight,
                        tint = ImdbWhite,
                        contentDescription = ""
                    )
                }
            }
        }

        item {
            // Where to watch section
            TextButton(
                onClick = onWhereToWatchClick,
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp)
                    .padding(top = 16.dp),
                contentPadding = PaddingValues(0.dp)
            ) {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "Where to watch",
                        color = ImdbWhite,
                        fontSize = 16.sp
                    )
                    Icon(
                        imageVector = Icons.Filled.ChevronRight,
                        tint = ImdbWhite,
                        contentDescription = ""
                    )
                }
            }
        }

        item {
            // Cast section
            Column(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(horizontal = 16.dp)
                    .padding(top = 24.dp)
            ) {
                Row(
                    modifier = Modifier.fillMaxWidth(),
                    horizontalArrangement = Arrangement.SpaceBetween,
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = "All Cast",
                        color = ImdbWhite,
                        fontSize = 18.sp,
                        fontWeight = FontWeight.Bold
                    )
                    TextButton(
                        onClick = { /* Navigate to full cast */ },
                        contentPadding = PaddingValues(0.dp)
                    ) {
                        Text(
                            text = "See All",
                            color = ImdbWhite,
                            fontSize = 14.sp
                        )
                    }
                }
                Spacer(modifier = Modifier.height(12.dp))
                LazyRow(
                    horizontalArrangement = Arrangement.spacedBy(12.dp),
                    contentPadding = PaddingValues(end = 16.dp)
                ) {
                    items(movieDetails.cast.take(10)) { castMember ->
                        CastMemberCard(
                            castMember = castMember,
                            onClick = { onCastMemberClick(castMember) }
                        )
                    }
                }
            }
        }
    }
}

@Composable
fun CastMemberCard(
    castMember: DetailedCastMember,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier
            .width(120.dp)
            .clickable { onClick() },
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        AsyncImage(
            model = castMember.profile,
            contentDescription = null,
            modifier = Modifier
                .size(100.dp)
                .clip(RoundedCornerShape(8.dp))
                .background(ImdbMediumGray),
            contentScale = ContentScale.Crop
        )
        Spacer(modifier = Modifier.height(8.dp))
        Text(
            text = castMember.name,
            color = ImdbWhite,
            fontSize = 12.sp,
            fontWeight = FontWeight.Medium,
            maxLines = 2,
            overflow = TextOverflow.Ellipsis
        )
        Text(
            text = castMember.character,
            color = ImdbWhite.copy(alpha = 0.7f),
            fontSize = 10.sp,
            maxLines = 2,
            overflow = TextOverflow.Ellipsis
        )
    }
}

// Preview data
private val sampleMovieDetails = MovieDetails(
    id = 24428,
    title = "The Avengers",
    originalTitle = "The Avengers",
    tagline = "Some assembly required.",
    year = 2012,
    releaseDate = "2012-04-25",
    rating = 7.7,
    voteCount = 28847,
    popularity = 118.864,
    poster = "https://image.tmdb.org/t/p/w500/RYMX2wcKCBAr24UyPD7xwmjaTn.jpg",
    backdrop = "https://image.tmdb.org/t/p/w1280/9BBTo63ANSmhC4e6r62OJFuK2GL.jpg",
    overview = "When an unexpected enemy emerges and threatens global safety and security, Nick Fury, director of the international peacekeeping agency known as S.H.I.E.L.D., finds himself in need of a team to pull the world back from the brink of disaster. Spanning the globe, a daring recruitment effort begins!",
    runtime = 143,
    budget = *********,
    revenue = 1518815515,
    status = "Released",
    adult = false,
    originalLanguage = "en",
    spokenLanguages = listOf(
        SpokenLanguage("en", "English"),
        SpokenLanguage("ru", "Pусский")
    ),
    genres = listOf(
        Genre(28, "Action"),
        Genre(12, "Adventure"),
        Genre(878, "Science Fiction")
    ),
    productionCompanies = listOf(
        ProductionCompany(420, "Marvel Studios", "https://image.tmdb.org/t/p/w500/hUzeosd33nzE5MCNsZxCGEKTXaQ.png", "US")
    ),
    productionCountries = listOf(
        ProductionCountry("US", "United States of America")
    ),
    cast = listOf(
        DetailedCastMember(3223, "Robert Downey Jr.", "Tony Stark / Iron Man", "52fe4751c3a36847f8024f49", 0, "https://image.tmdb.org/t/p/w500/5qHNjhtjMD4YWH3UP0rm4tKwxCL.jpg", "Acting", 58.132),
        DetailedCastMember(16828, "Chris Evans", "Steve Rogers / Captain America", "52fe4751c3a36847f8024f55", 1, "https://image.tmdb.org/t/p/w500/3bOGNsHlrswhyW79uvIHH1V43JI.jpg", "Acting", 49.388),
        DetailedCastMember(74568, "Chris Hemsworth", "Thor", "52fe4751c3a36847f8024f5b", 2, "https://image.tmdb.org/t/p/w500/jpurJ9jAcLCYjgHHfYF32m3zJYm.jpg", "Acting", 25.123)
    ),
    crew = listOf(
        CrewMember(15277, "Joss Whedon", "Director", "Directing", "52fe4751c3a36847f8024f61", "https://image.tmdb.org/t/p/w500/dTiVsuaTVTeGmvkhcyJvKp2A5kr.jpg", "Directing", 4.567)
    ),
    director = "Joss Whedon",
    directors = listOf(
        PersonSummary(15277, "Joss Whedon", "Director", "https://image.tmdb.org/t/p/w500/dTiVsuaTVTeGmvkhcyJvKp2A5kr.jpg")
    ),
    writers = listOf(
        PersonSummary(15277, "Joss Whedon", "Screenplay", "https://image.tmdb.org/t/p/w500/dTiVsuaTVTeGmvkhcyJvKp2A5kr.jpg")
    ),
    producers = listOf(
        PersonSummary(19292, "Kevin Feige", "Producer", "https://image.tmdb.org/t/p/w500/kJAchNnuaCx8YLbHYCZKVy4QSzr.jpg")
    ),
    similar = listOf(
        SimilarItem(1726, "Iron Man", 2008, "https://image.tmdb.org/t/p/w500/78lPtwv72eTNqFW9COBYI0dWDJa.jpg", 7.9, "After being held captive in an Afghan cave, billionaire engineer Tony Stark creates a unique weaponized suit of armor to fight evil."),
        SimilarItem(299536, "Avengers: Infinity War", 2018, "https://image.tmdb.org/t/p/w500/7WsyChQLEftFiDOVTGkv3hFpyyt.jpg", 8.3, "As the Avengers and their allies have continued to protect the world from threats too large for any one hero to handle, a new danger has emerged from the cosmic shadows: Thanos.")
    ),
    recommendations = listOf(
        SimilarItem(299537, "Avengers: Endgame", 2019, "https://image.tmdb.org/t/p/w500/or06FN3Dka5tukK1e9sl16pB3iy.jpg", 8.4, "After the devastating events of Avengers: Infinity War, the universe is in ruins due to the efforts of the Mad Titan, Thanos."),
        SimilarItem(299534, "Avengers: Age of Ultron", 2015, "https://image.tmdb.org/t/p/w500/4ssDuvEDkSArWEdyBl2X5EHvYKU.jpg", 7.3, "When Tony Stark tries to jumpstart a dormant peacekeeping program, things go awry and Earth's Mightiest Heroes are put to the ultimate test.")
    ),
    imdbId = "tt0848228",
    homepage = "https://www.marvel.com/movies/the-avengers",
    belongsToCollection = Collection(86311, "The Avengers Collection", "https://image.tmdb.org/t/p/w500/yFSIUVTCvgYrpalUktulvk3Gi5Y.jpg", "https://image.tmdb.org/t/p/w1280/zuW6fOiusv4X9nnW3paHGfXcSll.jpg")
)

private val sampleStreamingServices = listOf(
    StreamingService(1, "Netflix", "https://image.tmdb.org/t/p/w500/netflix.png", "https://netflix.com"),
    StreamingService(2, "Disney+", "https://image.tmdb.org/t/p/w500/disney.png", "https://disneyplus.com"),
    StreamingService(3, "Amazon Prime", "https://image.tmdb.org/t/p/w500/amazon.png", "https://amazon.com/prime")
)

@Preview(showBackground = true)
@Composable
fun MovieDetailsContentPreview() {
    ThyViewTheme {
        MovieDetailsContent(
            movieDetails = sampleMovieDetails,
            isInWatchlist = false,
            streamingServices = sampleStreamingServices,
            onWatchlistToggle = { },
            onCastMemberClick = { },
            onSeeAllReviewsClick = { },
            onRatingClick = { },
            onTrailerClick = { },
            onWhereToWatchClick = { }
        )
    }
}

@Preview(showBackground = true)
@Composable
fun MovieDetailsContentInWatchlistPreview() {
    ThyViewTheme {
        MovieDetailsContent(
            movieDetails = sampleMovieDetails,
            isInWatchlist = true,
            streamingServices = sampleStreamingServices,
            onWatchlistToggle = { },
            onCastMemberClick = { },
            onSeeAllReviewsClick = { },
            onRatingClick = { },
            onTrailerClick = { },
            onWhereToWatchClick = { }
        )
    }
}

@Preview(showBackground = true)
@Composable
fun CastMemberCardPreview() {
    ThyViewTheme {
        CastMemberCard(
            castMember = DetailedCastMember(
                id = 3223,
                name = "Robert Downey Jr.",
                character = "Tony Stark / Iron Man",
                creditId = "52fe4751c3a36847f8024f49",
                order = 0,
                profile = "https://image.tmdb.org/t/p/w500/5qHNjhtjMD4YWH3UP0rm4tKwxCL.jpg",
                knownForDepartment = "Acting",
                popularity = 58.132
            ),
            onClick = { }
        )
    }
}
