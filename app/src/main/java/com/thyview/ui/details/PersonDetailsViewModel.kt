package com.thyview.ui.details

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.thyview.models.search.PersonDetails
import com.thyview.services.SearchService
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

/**
 * ViewModel for person details screen
 */
@HiltViewModel
class PersonDetailsViewModel @Inject constructor(
    private val searchService: SearchService
) : ViewModel() {

    private val _uiState = MutableStateFlow(PersonDetailsUiState())
    val uiState: StateFlow<PersonDetailsUiState> = _uiState.asStateFlow()

    /**
     * Load person details
     */
    fun loadPersonDetails(personId: Int) {
        viewModelScope.launch {
            _uiState.update { it.copy(isLoading = true, error = null) }
            
            try {
                val result = searchService.getPersonDetails(personId)
                
                result.fold(
                    onSuccess = { personDetails ->
                        _uiState.update { 
                            it.copy(
                                personDetails = personDetails,
                                isLoading = false,
                                error = null
                            ) 
                        }
                        
                        Timber.d("Person details loaded successfully for ID: $personId")
                    },
                    onFailure = { exception ->
                        _uiState.update { 
                            it.copy(
                                isLoading = false,
                                error = exception.message ?: "Failed to load person details"
                            ) 
                        }
                        
                        Timber.e(exception, "Failed to load person details for ID: $personId")
                    }
                )
            } catch (e: Exception) {
                _uiState.update { 
                    it.copy(
                        isLoading = false,
                        error = e.message ?: "Unexpected error occurred"
                    ) 
                }
                
                Timber.e(e, "Unexpected error loading person details for ID: $personId")
            }
        }
    }
}

/**
 * UI state for person details screen
 */
data class PersonDetailsUiState(
    val personDetails: PersonDetails? = null,
    val isLoading: Boolean = false,
    val error: String? = null
)
