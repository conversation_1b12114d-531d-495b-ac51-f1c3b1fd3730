package com.thyview.ui.details

import android.content.Intent
import android.net.Uri
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.Favorite
import androidx.compose.material.icons.filled.FavoriteBorder
import androidx.compose.material.icons.filled.PlayArrow
import androidx.compose.material.icons.filled.Star
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.tooling.preview.Preview
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavController
import coil.compose.AsyncImage
import com.thyview.models.search.CastMember
import com.thyview.models.search.TVDetails
import com.thyview.ui.ThyViewTheme
import com.thyview.ui.ImdbBlack
import com.thyview.ui.ImdbYellow
import com.thyview.ui.ImdbWhite
import com.thyview.ui.ImdbDarkGray
import com.thyview.ui.ImdbMediumGray

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun TVDetailsScreen(
    tvShowId: Int,
    navController: NavController,
    viewModel: TVDetailsViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsState()
    val context = LocalContext.current

    LaunchedEffect(tvShowId) {
        viewModel.loadTVDetails(tvShowId)
    }

    ThyViewTheme {
        Scaffold(
            topBar = {
                TopAppBar(
                    title = { 
                        Text(
                            text = uiState.tvDetails?.name ?: "TV Show Details",
                            color = ImdbYellow,
                            maxLines = 1,
                            overflow = TextOverflow.Ellipsis
                        ) 
                    },
                    navigationIcon = {
                        IconButton(onClick = { navController.navigateUp() }) {
                            Icon(
                                imageVector = Icons.Default.ArrowBack,
                                contentDescription = "Back",
                                tint = ImdbYellow
                            )
                        }
                    },
                    colors = TopAppBarDefaults.topAppBarColors(
                        containerColor = ImdbBlack,
                        titleContentColor = ImdbYellow
                    )
                )
            },
            containerColor = ImdbBlack
        ) { innerPadding ->
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(innerPadding)
                    .background(ImdbBlack)
            ) {
                when {
                    uiState.isLoading -> {
                        CircularProgressIndicator(
                            modifier = Modifier.align(Alignment.Center),
                            color = ImdbYellow
                        )
                    }
                    
                    uiState.error != null -> {
                        Column(
                            modifier = Modifier.align(Alignment.Center),
                            horizontalAlignment = Alignment.CenterHorizontally
                        ) {
                            Text(
                                text = "Technical error, try again later",
                                color = ImdbWhite,
                                fontSize = 16.sp
                            )
                            Spacer(modifier = Modifier.height(8.dp))
                            Text(
                                text = uiState.error ?: "Unknown error",
                                color = ImdbWhite.copy(alpha = 0.7f),
                                fontSize = 12.sp
                            )
                        }
                    }
                    
                    uiState.tvDetails != null -> {
                        val tvDetails = uiState.tvDetails
                        TVDetailsContent(
                            tvDetails = tvDetails!!,
                            isInWatchlist = uiState.isInWatchlist,
                            onWatchlistToggle = { viewModel.toggleWatchlist() },
                            onCastMemberClick = { castMember ->
                                navController.navigate("person_details/${castMember.id}")
                            },
                            onSeeAllReviewsClick = {
                                navController.navigate("reviews?tv=${tvDetails?.name}")
                            },
                            onRatingClick = {
                                navController.navigate("reviews?tv=${tvDetails?.name}")
                            },
                            onTrailerClick = {
                                val intent = Intent(Intent.ACTION_VIEW, Uri.parse("https://www.youtube.com/watch?v=dQw4w9WgXcQ"))
                                context.startActivity(intent)
                            },
                            onWhereToWatchClick = {
                                navController.navigate("streaming_services/${tvShowId}/tv")
                            }
                        )
                    }
                }
            }
        }
    }
}

@Composable
fun TVDetailsContent(
    tvDetails: TVDetails,
    isInWatchlist: Boolean,
    onWatchlistToggle: () -> Unit,
    onCastMemberClick: (CastMember) -> Unit,
    onSeeAllReviewsClick: () -> Unit,
    onRatingClick: () -> Unit,
    onTrailerClick: () -> Unit,
    onWhereToWatchClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    LazyColumn(
        modifier = modifier.fillMaxSize(),
        contentPadding = PaddingValues(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        item {
            // TV show poster and basic info
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                AsyncImage(
                    model = tvDetails.poster,
                    contentDescription = null,
                    modifier = Modifier
                        .width(120.dp)
                        .height(180.dp)
                        .clip(RoundedCornerShape(8.dp))
                        .background(ImdbMediumGray),
                    contentScale = ContentScale.Crop
                )
                
                Column(
                    modifier = Modifier.weight(1f),
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    Text(
                        text = tvDetails.name,
                        color = ImdbWhite,
                        fontSize = 20.sp,
                        fontWeight = FontWeight.Bold
                    )
                    
                    Text(
                        text = "${tvDetails.releaseYear} • ${tvDetails.seasons} seasons • ${tvDetails.episodes} episodes",
                        color = ImdbWhite.copy(alpha = 0.8f),
                        fontSize = 14.sp
                    )
                    
                    Row(
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Icon(
                            imageVector = Icons.Default.Star,
                            contentDescription = null,
                            tint = ImdbYellow,
                            modifier = Modifier.size(16.dp)
                        )
                        Spacer(modifier = Modifier.width(4.dp))
                        Text(
                            text = String.format("%.1f", tvDetails.rating),
                            color = ImdbWhite,
                            fontSize = 14.sp,
                            fontWeight = FontWeight.Medium
                        )
                    }
                    
                    Text(
                        text = tvDetails.genres.joinToString(" • "),
                        color = ImdbYellow,
                        fontSize = 12.sp
                    )
                    
                    Text(
                        text = "Status: ${tvDetails.status}",
                        color = ImdbWhite.copy(alpha = 0.7f),
                        fontSize = 12.sp
                    )
                }
            }
        }
        
        item {
            // Action buttons
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Button(
                    onClick = onWatchlistToggle,
                    modifier = Modifier.weight(1f),
                    colors = ButtonDefaults.buttonColors(
                        containerColor = if (isInWatchlist) ImdbYellow else ImdbDarkGray
                    )
                ) {
                    Icon(
                        imageVector = if (isInWatchlist) Icons.Default.Favorite else Icons.Default.FavoriteBorder,
                        contentDescription = null,
                        tint = if (isInWatchlist) ImdbBlack else ImdbWhite
                    )
                    Spacer(modifier = Modifier.width(8.dp))
                    Text(
                        text = if (isInWatchlist) "In Watchlist" else "Add to Watchlist",
                        color = if (isInWatchlist) ImdbBlack else ImdbWhite
                    )
                }
                
                Button(
                    onClick = onTrailerClick,
                    colors = ButtonDefaults.buttonColors(containerColor = ImdbYellow)
                ) {
                    Icon(
                        imageVector = Icons.Default.PlayArrow,
                        contentDescription = null,
                        tint = ImdbBlack
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Text(
                        text = "Trailer",
                        color = ImdbBlack
                    )
                }
            }
        }

        item {
            // Description
            var isExpanded by remember { mutableStateOf(false) }

            Card(
                modifier = Modifier.fillMaxWidth(),
                colors = CardDefaults.cardColors(containerColor = ImdbDarkGray)
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Text(
                        text = "Overview",
                        color = ImdbYellow,
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Medium
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(
                        text = tvDetails.overview,
                        color = ImdbWhite,
                        fontSize = 14.sp,
                        maxLines = if (isExpanded) Int.MAX_VALUE else 5,
                        overflow = TextOverflow.Ellipsis
                    )
                    if (tvDetails.overview.length > 200) {
                        Spacer(modifier = Modifier.height(8.dp))
                        TextButton(
                            onClick = { isExpanded = !isExpanded }
                        ) {
                            Text(
                                text = if (isExpanded) "Show less" else "Show more",
                                color = ImdbYellow
                            )
                        }
                    }
                }
            }
        }

        item {
            // Cast section
            if (tvDetails.cast.isNotEmpty()) {
                Text(
                    text = "Cast",
                    color = ImdbYellow,
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold
                )
                Spacer(modifier = Modifier.height(8.dp))
                LazyRow(
                    horizontalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    items(tvDetails.cast.take(10)) { castMember ->
                        TVCastMemberCard(
                            castMember = castMember,
                            onClick = { onCastMemberClick(castMember) }
                        )
                    }
                }
            }
        }

        item {
            // Reviews and rating section
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                Button(
                    onClick = onSeeAllReviewsClick,
                    modifier = Modifier.weight(1f),
                    colors = ButtonDefaults.buttonColors(containerColor = ImdbDarkGray)
                ) {
                    Text(
                        text = "See all reviews",
                        color = ImdbWhite
                    )
                }

                Button(
                    onClick = onRatingClick,
                    colors = ButtonDefaults.buttonColors(containerColor = ImdbDarkGray)
                ) {
                    Icon(
                        imageVector = Icons.Default.Star,
                        contentDescription = null,
                        tint = ImdbYellow
                    )
                    Spacer(modifier = Modifier.width(4.dp))
                    Text(
                        text = "Rate",
                        color = ImdbWhite
                    )
                }
            }
        }

        item {
            // Where to watch section
            Button(
                onClick = onWhereToWatchClick,
                modifier = Modifier.fillMaxWidth(),
                colors = ButtonDefaults.buttonColors(containerColor = ImdbYellow)
            ) {
                Text(
                    text = "Where to watch",
                    color = ImdbBlack,
                    fontWeight = FontWeight.Medium
                )
            }
        }
    }
}

@Composable
fun TVCastMemberCard(
    castMember: CastMember,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier
            .width(100.dp)
            .clickable { onClick() },
        colors = CardDefaults.cardColors(containerColor = ImdbDarkGray)
    ) {
        Column(
            modifier = Modifier.padding(8.dp),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            AsyncImage(
                model = castMember.profilePath,
                contentDescription = null,
                modifier = Modifier
                    .size(80.dp)
                    .clip(RoundedCornerShape(8.dp))
                    .background(ImdbMediumGray),
                contentScale = ContentScale.Crop
            )
            Spacer(modifier = Modifier.height(8.dp))
            Text(
                text = castMember.name,
                color = ImdbWhite,
                fontSize = 12.sp,
                fontWeight = FontWeight.Medium,
                maxLines = 2,
                overflow = TextOverflow.Ellipsis
            )
            Text(
                text = castMember.character,
                color = ImdbWhite.copy(alpha = 0.7f),
                fontSize = 10.sp,
                maxLines = 2,
                overflow = TextOverflow.Ellipsis
            )
        }
    }
}

// Preview data
private val sampleTVDetails = TVDetails(
    id = 66732,
    name = "Stranger Things",
    releaseYear = 2016,
    poster = "https://image.tmdb.org/t/p/w500/x2LSRK2Cm7MZhjluni1msVJ3wDF.jpg",
    overview = "When a young boy vanishes, a small town uncovers a mystery involving secret experiments, terrifying supernatural forces, and one strange little girl.",
    rating = 8.6,
    genres = listOf("Sci-Fi & Fantasy", "Mystery", "Drama"),
    seasons = 4,
    episodes = 42,
    status = "Ended",
    creators = listOf("The Duffer Brothers"),
    cast = listOf(
        CastMember(4, "Millie Bobby Brown", "Eleven", "https://image.tmdb.org/t/p/w500/qZtAf4Z1lazGQoYVXiHOrvLr5lI.jpg"),
        CastMember(5, "Finn Wolfhard", "Mike Wheeler", "https://image.tmdb.org/t/p/w500/sKzTke6d6xVYbhcFbGKKRPGqhJH.jpg"),
        CastMember(6, "Gaten Matarazzo", "Dustin Henderson", "https://image.tmdb.org/t/p/w500/o4Ql8QHHGhYGgUmBCBrXoKkUHaH.jpg"),
        CastMember(7, "Caleb McLaughlin", "Lucas Sinclair", "https://image.tmdb.org/t/p/w500/tP5RubyaKSJJtAzFQBmNhIhNNxe.jpg"),
        CastMember(8, "Noah Schnapp", "Will Byers", "https://image.tmdb.org/t/p/w500/3GSWWrqQgcZiiOaVcjpZFHdnhQu.jpg")
    )
)

@Preview(showBackground = true)
@Composable
fun TVDetailsContentPreview() {
    ThyViewTheme {
        TVDetailsContent(
            tvDetails = sampleTVDetails,
            isInWatchlist = false,
            onWatchlistToggle = { },
            onCastMemberClick = { },
            onSeeAllReviewsClick = { },
            onRatingClick = { },
            onTrailerClick = { },
            onWhereToWatchClick = { }
        )
    }
}

@Preview(showBackground = true)
@Composable
fun TVDetailsContentInWatchlistPreview() {
    ThyViewTheme {
        TVDetailsContent(
            tvDetails = sampleTVDetails,
            isInWatchlist = true,
            onWatchlistToggle = { },
            onCastMemberClick = { },
            onSeeAllReviewsClick = { },
            onRatingClick = { },
            onTrailerClick = { },
            onWhereToWatchClick = { }
        )
    }
}

@Preview(showBackground = true)
@Composable
fun TVCastMemberCardPreview() {
    ThyViewTheme {
        TVCastMemberCard(
            castMember = CastMember(
                id = 4,
                name = "Millie Bobby Brown",
                character = "Eleven",
                profilePath = "https://image.tmdb.org/t/p/w500/qZtAf4Z1lazGQoYVXiHOrvLr5lI.jpg"
            ),
            onClick = { }
        )
    }
}
