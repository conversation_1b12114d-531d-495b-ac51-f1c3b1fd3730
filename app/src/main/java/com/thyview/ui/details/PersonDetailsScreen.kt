package com.thyview.ui.details

import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.LazyRow
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.ArrowBack
import androidx.compose.material.icons.filled.CalendarToday
import androidx.compose.material.icons.filled.LocationOn
import androidx.compose.material.icons.filled.Person
import androidx.compose.material.icons.filled.PersonAdd
import androidx.compose.material.icons.filled.PersonRemove
import androidx.compose.material.icons.filled.Star
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.tooling.preview.Preview
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavController
import coil.compose.AsyncImage
import com.thyview.R
import com.thyview.models.search.CreditItem
import com.thyview.models.search.PersonDetails
import com.thyview.models.search.Credits
import com.thyview.ui.ThyViewTheme
import com.thyview.ui.ImdbBlack
import com.thyview.ui.ImdbYellow
import com.thyview.ui.ImdbWhite
import com.thyview.ui.ImdbDarkGray
import com.thyview.ui.ImdbMediumGray
import java.text.SimpleDateFormat
import java.util.*

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun PersonDetailsScreen(
    personId: Int,
    navController: NavController,
    viewModel: PersonDetailsViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsState()

    LaunchedEffect(personId) {
        viewModel.loadPersonDetails(personId)
    }

    ThyViewTheme {
        Scaffold(
            topBar = {
                TopAppBar(
                    title = { 
                        Text(
                            text = uiState.personDetails?.name ?: "Person Details",
                            color = ImdbYellow,
                            maxLines = 1,
                            overflow = TextOverflow.Ellipsis
                        ) 
                    },
                    navigationIcon = {
                        IconButton(onClick = { navController.navigateUp() }) {
                            Icon(
                                imageVector = Icons.Default.ArrowBack,
                                contentDescription = "Back",
                                tint = ImdbYellow
                            )
                        }
                    },
                    colors = TopAppBarDefaults.topAppBarColors(
                        containerColor = ImdbBlack,
                        titleContentColor = ImdbYellow
                    )
                )
            },
            containerColor = ImdbBlack
        ) { innerPadding ->
            Box(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(innerPadding)
                    .background(ImdbBlack)
            ) {
                when {
                    uiState.isLoading -> {
                        CircularProgressIndicator(
                            modifier = Modifier.align(Alignment.Center),
                            color = ImdbYellow
                        )
                    }
                    
                    uiState.error != null -> {
                        Column(
                            modifier = Modifier.align(Alignment.Center),
                            horizontalAlignment = Alignment.CenterHorizontally
                        ) {
                            Text(
                                text = "Technical error, try again later",
                                color = ImdbWhite,
                                fontSize = 16.sp
                            )
                            Spacer(modifier = Modifier.height(8.dp))
                            Text(
                                text = uiState.error ?: "Unknown error",
                                color = ImdbWhite.copy(alpha = 0.7f),
                                fontSize = 12.sp
                            )
                        }
                    }
                    
                    uiState.personDetails != null -> {
                        val personDetails = uiState.personDetails
                        PersonDetailsContent(
                            personDetails = personDetails!!,
                            onCreditClick = { credit ->
                                when {
                                    credit.title != null -> navController.navigate("movie_details/${credit.id}")
                                    credit.name != null -> navController.navigate("tv_details/${credit.id}")
                                }
                            }
                        )
                    }
                }
            }
        }
    }
}

@Composable
fun CreditCard(
    credit: CreditItem,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier
            .width(120.dp)
            .clickable { onClick() },
        colors = CardDefaults.cardColors(containerColor = ImdbDarkGray)
    ) {
        Column(
            modifier = Modifier.padding(8.dp)
        ) {
            AsyncImage(
                model = credit.poster,
                contentDescription = null,
                modifier = Modifier
                    .fillMaxWidth()
                    .height(160.dp)
                    .clip(RoundedCornerShape(8.dp))
                    .background(ImdbMediumGray),
                contentScale = ContentScale.Crop
            )
            Spacer(modifier = Modifier.height(8.dp))
            Text(
                text = credit.title ?: credit.name ?: "Unknown",
                color = ImdbWhite,
                fontSize = 12.sp,
                fontWeight = FontWeight.Medium,
                maxLines = 2,
                overflow = TextOverflow.Ellipsis
            )
            credit.year?.let { year ->
                Text(
                    text = year.toString(),
                    color = ImdbWhite.copy(alpha = 0.7f),
                    fontSize = 10.sp
                )
            }
            if (credit.rating > 0) {
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Icon(
                        imageVector = Icons.Default.Star,
                        contentDescription = null,
                        tint = ImdbYellow,
                        modifier = Modifier.size(12.dp)
                    )
                    Spacer(modifier = Modifier.width(2.dp))
                    Text(
                        text = String.format("%.1f", credit.rating),
                        color = ImdbYellow,
                        fontSize = 10.sp
                    )
                }
            }
            credit.character?.let { character ->
                Text(
                    text = character,
                    color = ImdbWhite.copy(alpha = 0.6f),
                    fontSize = 9.sp,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )
            }
        }
    }
}

/**
 * Format date string for display
 */
private fun formatDate(dateString: String): String {
    return try {
        val inputFormat = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())
        val outputFormat = SimpleDateFormat("MMM dd, yyyy", Locale.getDefault())
        val date = inputFormat.parse(dateString)
        outputFormat.format(date ?: Date())
    } catch (e: Exception) {
        dateString
    }
}

/**
 * Format followers count for display (e.g., 1.2M, 45.6K)
 */
private fun formatFollowersCount(popularity: Double): String {
    // Convert popularity to a realistic followers count
    val followers = (popularity * 25000).toLong() // Scale popularity to followers

    return when {
        followers >= 1_000_000 -> {
            val millions = followers / 1_000_000.0
            String.format("%.1fM", millions)
        }
        followers >= 1_000 -> {
            val thousands = followers / 1_000.0
            String.format("%.1fK", thousands)
        }
        else -> followers.toString()
    }
}

@Composable
fun PersonDetailsContent(
    personDetails: PersonDetails,
    onCreditClick: (CreditItem) -> Unit,
    modifier: Modifier = Modifier
) {
    // State for follow functionality
    var isFollowing by remember { mutableStateOf(false) }
    LazyColumn(
        modifier = modifier.fillMaxSize(),
        contentPadding = PaddingValues(16.dp),
        verticalArrangement = Arrangement.spacedBy(16.dp)
    ) {
        item {
            // Person profile and basic info
            Row(
                modifier = Modifier.fillMaxWidth(),
                horizontalArrangement = Arrangement.spacedBy(16.dp)
            ) {
                AsyncImage(
                    model = personDetails.profile,
                    contentDescription = null,
                    modifier = Modifier
                        .width(120.dp)
                        .height(180.dp)
                        .clip(RoundedCornerShape(8.dp))
                        .background(ImdbMediumGray),
                    contentScale = ContentScale.Crop
                )
                
                Column(
                    modifier = Modifier.weight(1f),
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    Text(
                        text = personDetails.name,
                        color = ImdbWhite,
                        fontSize = 20.sp,
                        fontWeight = FontWeight.Bold
                    )

                    Row(
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Icon(
                            imageVector = Icons.Default.Person,
                            contentDescription = null,
                            tint = ImdbWhite,
                            modifier = Modifier.size(16.dp)
                        )
                        Spacer(modifier = Modifier.width(4.dp))
                        Text(
                            text = personDetails.knownForDepartment,
                            color = ImdbWhite,
                            fontSize = 14.sp,
                            fontWeight = FontWeight.Medium
                        )
                    }

                    // Social features section
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.spacedBy(12.dp),
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        // Follow button
                        Button(
                            onClick = { isFollowing = !isFollowing },
                            modifier = Modifier.height(36.dp),
                            colors = ButtonDefaults.buttonColors(
                                containerColor = if (isFollowing) ImdbDarkGray else ImdbYellow,
                                contentColor = if (isFollowing) ImdbWhite else ImdbBlack
                            ),
                            shape = RoundedCornerShape(18.dp),
                            contentPadding = PaddingValues(horizontal = 16.dp, vertical = 8.dp)
                        ) {
                            Text(
                                text = if (isFollowing) "Fanned" else "Fan",
                                fontSize = 12.sp,
                                fontWeight = FontWeight.Medium
                            )
                        }

                        // Followers count
                        Text(
                            text = "${formatFollowersCount(personDetails.popularity)} followers",
                            color = ImdbWhite.copy(alpha = 0.8f),
                            fontSize = 12.sp,
                            fontWeight = FontWeight.Medium
                        )
                    }
                    
                    personDetails.birthday?.let { birthday ->
                        Row(
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Icon(
                                imageVector = Icons.Default.CalendarToday,
                                contentDescription = null,
                                tint = ImdbWhite.copy(alpha = 0.7f),
                                modifier = Modifier.size(14.dp)
                            )
                            Spacer(modifier = Modifier.width(4.dp))
                            Text(
                                text = formatDate(birthday),
                                color = ImdbWhite.copy(alpha = 0.8f),
                                fontSize = 12.sp
                            )
                        }
                    }
                    
                    personDetails.placeOfBirth?.let { birthPlace ->
                        Row(
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Icon(
                                imageVector = Icons.Default.LocationOn,
                                contentDescription = null,
                                tint = ImdbWhite.copy(alpha = 0.7f),
                                modifier = Modifier.size(14.dp)
                            )
                            Spacer(modifier = Modifier.width(4.dp))
                            Text(
                                text = birthPlace,
                                color = ImdbWhite.copy(alpha = 0.8f),
                                fontSize = 12.sp,
                                maxLines = 2,
                                overflow = TextOverflow.Ellipsis
                            )
                        }
                    }
                    
                    // Also known as
                    if (personDetails.alsoKnownAs.isNotEmpty()) {
                        Text(
                            text = "Also known as:",
                            color = ImdbWhite.copy(alpha = 0.7f),
                            fontSize = 12.sp,
                            fontWeight = FontWeight.Medium
                        )
                        Text(
                            text = personDetails.alsoKnownAs.take(3).joinToString(", "),
                            color = ImdbWhite.copy(alpha = 0.8f),
                            fontSize = 11.sp,
                            maxLines = 2,
                            overflow = TextOverflow.Ellipsis
                        )
                    }
                }
            }
        }
        
        item {
            // Biography
            if (personDetails.biography.isNotBlank()) {
                var isExpanded by remember { mutableStateOf(false) }
                var showMoreButton by remember { mutableStateOf(false) }

                Card(
                    modifier = Modifier.fillMaxWidth(),
                    colors = CardDefaults.cardColors(containerColor = ImdbDarkGray)
                ) {
                    Column(
                        modifier = Modifier.padding(16.dp)
                    ) {
                        Text(
                            text = "Biography",
                            color = ImdbWhite,
                            fontSize = 16.sp,
                            fontWeight = FontWeight.Medium,
                            modifier = Modifier.align(Alignment.Start)
                        )
                        Spacer(modifier = Modifier.height(8.dp))

                        // Biography text with improved truncation detection
                        Text(
                            text = personDetails.biography,
                            color = ImdbWhite,
                            fontSize = 14.sp,
                            maxLines = if (isExpanded) Int.MAX_VALUE else 5,
                            overflow = TextOverflow.Ellipsis,
                            onTextLayout = { textLayoutResult ->
                                // Check if text is actually truncated
                                showMoreButton = textLayoutResult.hasVisualOverflow
                            }
                        )

                        // Show more/less button only if text is actually truncated
                        if (showMoreButton || isExpanded) {
                            Spacer(modifier = Modifier.height(8.dp))
                            TextButton(
                                onClick = { isExpanded = !isExpanded },
                                colors = ButtonDefaults.textButtonColors(
                                    contentColor = ImdbWhite
                                ),
                                modifier = Modifier.align(Alignment.Start),
                                contentPadding = PaddingValues(0.dp)
                            ) {
                                Text(
                                    text = if (isExpanded) "Show less" else "Show more",
                                    fontSize = 14.sp,
                                    fontWeight = FontWeight.Medium
                                )
                            }
                        }
                    }
                }
            }
        }

        item {
            // Movie Credits
            if (personDetails.movieCredits.cast.isNotEmpty()) {
                Text(
                    text = "Movies",
                    color = ImdbWhite,
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold
                )
                Spacer(modifier = Modifier.height(8.dp))
                LazyRow(
                    horizontalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    items(personDetails.movieCredits.cast.take(10)) { credit ->
                        CreditCard(
                            credit = credit,
                            onClick = { onCreditClick(credit) }
                        )
                    }
                }
            }
        }

        item {
            // TV Credits
            if (personDetails.tvCredits.cast.isNotEmpty()) {
                Text(
                    text = "TV Shows",
                    color = ImdbWhite,
                    fontSize = 18.sp,
                    fontWeight = FontWeight.Bold
                )
                Spacer(modifier = Modifier.height(8.dp))
                LazyRow(
                    horizontalArrangement = Arrangement.spacedBy(12.dp)
                ) {
                    items(personDetails.tvCredits.cast.take(10)) { credit ->
                        CreditCard(
                            credit = credit,
                            onClick = { onCreditClick(credit) }
                        )
                    }
                }
            }
        }

        item {
            // Career Highlights
            Card(
                modifier = Modifier.fillMaxWidth(),
                colors = CardDefaults.cardColors(containerColor = ImdbDarkGray)
            ) {
                Column(
                    modifier = Modifier.padding(16.dp)
                ) {
                    Text(
                        text = "Career Highlights",
                        color = ImdbWhite,
                        fontSize = 16.sp,
                        fontWeight = FontWeight.Medium
                    )
                    Spacer(modifier = Modifier.height(8.dp))

                    // Total movies and TV shows
                    Row(
                        modifier = Modifier.fillMaxWidth(),
                        horizontalArrangement = Arrangement.SpaceBetween
                    ) {
                        Column {
                            Text(
                                text = "${personDetails.movieCredits.cast.size}",
                                color = ImdbWhite,
                                fontSize = 20.sp,
                                fontWeight = FontWeight.Bold
                            )
                            Text(
                                text = "Movies",
                                color = ImdbWhite.copy(alpha = 0.8f),
                                fontSize = 12.sp
                            )
                        }

                        Column {
                            Text(
                                text = "${personDetails.tvCredits.cast.size}",
                                color = ImdbWhite,
                                fontSize = 20.sp,
                                fontWeight = FontWeight.Bold
                            )
                            Text(
                                text = "TV Shows",
                                color = ImdbWhite.copy(alpha = 0.8f),
                                fontSize = 12.sp
                            )
                        }

                        Column {
                            val highestRated = (personDetails.movieCredits.cast + personDetails.tvCredits.cast)
                                .maxByOrNull { it.rating }
                            Text(
                                text = if (highestRated != null) String.format("%.1f", highestRated.rating) else "N/A",
                                color = ImdbWhite,
                                fontSize = 20.sp,
                                fontWeight = FontWeight.Bold
                            )
                            Text(
                                text = "Highest Rated",
                                color = ImdbWhite.copy(alpha = 0.8f),
                                fontSize = 12.sp
                            )
                        }
                    }
                }
            }
        }
    }
}

// Preview data
private val samplePersonDetails = PersonDetails(
    id = 6193,
    name = "Leonardo DiCaprio",
    alsoKnownAs = listOf("Leo", "Leonardo Wilhelm DiCaprio"),
    biography = "Leonardo Wilhelm DiCaprio is an American actor and film producer. Known for his work in biopics and period films, DiCaprio has received numerous accolades throughout his career, including an Academy Award, a British Academy Film Award, and three Golden Globe Awards. As of 2019, his films have grossed over $7.2 billion worldwide, and he has been placed eight times in annual rankings of the world's highest-paid actors. DiCaprio began his career in the late 1980s by appearing in television commercials, after which he had recurring roles in various television series such as the sitcom Growing Pains and the soap opera Santa Barbara. He made his film debut in the comedic sci-fi horror film Critters 3 (1991) and received first notable critical praise for his performance in This Boy's Life (1993). DiCaprio achieved international stardom with the star-crossed romances Romeo + Juliet (1996) and Titanic (1997).",
    birthday = "1974-11-11",
    deathday = null,
    placeOfBirth = "Los Angeles, California, USA",
    knownForDepartment = "Acting",
    gender = 2,
    popularity = 45.123,
    adult = false,
    imdbId = "nm0000138",
    homepage = "https://www.leonardodicaprio.org/",
    profile = "https://image.tmdb.org/t/p/w500/wo2hJpn04vbtmh0B9utCFdsQhxM.jpg",
    knownFor = listOf("Titanic", "Inception", "The Revenant", "The Wolf of Wall Street"),
    movieCredits = Credits(
        cast = listOf(
            CreditItem(
                id = 597,
                title = "Titanic",
                character = "Jack Dawson",
                year = 1997,
                poster = "https://image.tmdb.org/t/p/w500/9xjZS2rlVxm8SFx8kPC3aIGCOYQ.jpg",
                rating = 7.9,
                popularity = 85.234,
                creditId = "52fe4284c3a36847f8014a11"
            ),
            CreditItem(
                id = 27205,
                title = "Inception",
                character = "Dom Cobb",
                year = 2010,
                poster = "https://image.tmdb.org/t/p/w500/ljsZTbVsrQSqZgWeep2B1QiDKuh.jpg",
                rating = 8.4,
                popularity = 92.567,
                creditId = "52fe4284c3a36847f8014a15"
            ),
            CreditItem(
                id = 281957,
                title = "The Revenant",
                character = "Hugh Glass",
                year = 2015,
                poster = "https://image.tmdb.org/t/p/w500/tSaBkriE7TpbjFoQUFXuikoz0dF.jpg",
                rating = 8.0,
                popularity = 78.901,
                creditId = "52fe4284c3a36847f8014a19"
            )
        ),
        crew = emptyList()
    ),
    tvCredits = Credits(
        cast = listOf(
            CreditItem(
                id = 1234,
                name = "Growing Pains",
                character = "Luke Brower",
                year = 1991,
                poster = "https://image.tmdb.org/t/p/w500/sample_tv_poster.jpg",
                rating = 6.5,
                popularity = 12.345,
                creditId = "52fe4284c3a36847f8014a20",
                episodeCount = 23
            )
        ),
        crew = emptyList()
    )
)

@Preview(showBackground = true)
@Composable
fun PersonDetailsContentPreview() {
    ThyViewTheme {
        PersonDetailsContent(
            personDetails = samplePersonDetails,
            onCreditClick = { }
        )
    }
}

@Preview(showBackground = true)
@Composable
fun CreditCardPreview() {
    ThyViewTheme {
        CreditCard(
            credit = CreditItem(
                id = 597,
                title = "Titanic",
                character = "Jack Dawson",
                year = 1997,
                poster = "https://image.tmdb.org/t/p/w500/9xjZS2rlVxm8SFx8kPC3aIGCOYQ.jpg",
                rating = 7.9,
                popularity = 85.234,
                creditId = "52fe4284c3a36847f8014a11"
            ),
            onClick = { }
        )
    }
}

@Preview(showBackground = true)
@Composable
fun CreditCardTVPreview() {
    ThyViewTheme {
        CreditCard(
            credit = CreditItem(
                id = 1234,
                name = "Growing Pains",
                character = "Luke Brower",
                year = 1991,
                poster = "https://image.tmdb.org/t/p/w500/sample_tv_poster.jpg",
                rating = 6.5,
                popularity = 12.345,
                creditId = "52fe4284c3a36847f8014a20",
                episodeCount = 23
            ),
            onClick = { }
        )
    }
}
