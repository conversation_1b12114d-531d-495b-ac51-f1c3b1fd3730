package com.thyview.ui.details

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.thyview.models.search.TVDetails
import com.thyview.services.SearchService
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

/**
 * ViewModel for TV show details screen
 */
@HiltViewModel
class TVDetailsViewModel @Inject constructor(
    private val searchService: SearchService
) : ViewModel() {

    private val _uiState = MutableStateFlow(TVDetailsUiState())
    val uiState: StateFlow<TVDetailsUiState> = _uiState.asStateFlow()

    /**
     * Load TV show details and related data
     */
    fun loadTVDetails(tvShowId: Int) {
        viewModelScope.launch {
            _uiState.update { it.copy(isLoading = true, error = null) }
            
            try {
                // Load TV details
                val tvResult = searchService.getTVDetails(tvShowId)
                
                tvResult.fold(
                    onSuccess = { tvDetails ->
                        _uiState.update { 
                            it.copy(
                                tvDetails = tvDetails,
                                isLoading = false,
                                error = null
                            ) 
                        }
                        
                        // Load additional data in parallel
                        loadWatchlistStatus(tvShowId)
                        
                        Timber.d("TV details loaded successfully for ID: $tvShowId")
                    },
                    onFailure = { exception ->
                        _uiState.update { 
                            it.copy(
                                isLoading = false,
                                error = exception.message ?: "Failed to load TV show details"
                            ) 
                        }
                        
                        Timber.e(exception, "Failed to load TV details for ID: $tvShowId")
                    }
                )
            } catch (e: Exception) {
                _uiState.update { 
                    it.copy(
                        isLoading = false,
                        error = e.message ?: "Unexpected error occurred"
                    ) 
                }
                
                Timber.e(e, "Unexpected error loading TV details for ID: $tvShowId")
            }
        }
    }

    /**
     * Toggle watchlist status
     */
    fun toggleWatchlist() {
        val currentState = _uiState.value
        val tvDetails = currentState.tvDetails ?: return
        
        viewModelScope.launch {
            try {
                val result = searchService.toggleWatchlist(
                    contentId = tvDetails.id,
                    contentType = "tv",
                    isCurrentlyInWatchlist = currentState.isInWatchlist
                )
                
                result.fold(
                    onSuccess = { response ->
                        _uiState.update { 
                            it.copy(isInWatchlist = response.isInWatchlist) 
                        }
                        
                        Timber.d("Watchlist toggled successfully for TV ID: ${tvDetails.id}")
                    },
                    onFailure = { exception ->
                        Timber.e(exception, "Failed to toggle watchlist for TV ID: ${tvDetails.id}")
                    }
                )
            } catch (e: Exception) {
                Timber.e(e, "Unexpected error toggling watchlist for TV ID: ${tvDetails.id}")
            }
        }
    }

    /**
     * Load watchlist status
     */
    private fun loadWatchlistStatus(tvShowId: Int) {
        viewModelScope.launch {
            try {
                val result = searchService.isInWatchlist("tv", tvShowId)
                
                result.fold(
                    onSuccess = { isInWatchlist ->
                        _uiState.update { it.copy(isInWatchlist = isInWatchlist) }
                        Timber.d("Watchlist status loaded for TV ID: $tvShowId")
                    },
                    onFailure = { exception ->
                        Timber.e(exception, "Failed to load watchlist status for TV ID: $tvShowId")
                    }
                )
            } catch (e: Exception) {
                Timber.e(e, "Unexpected error loading watchlist status for TV ID: $tvShowId")
            }
        }
    }
}

/**
 * UI state for TV details screen
 */
data class TVDetailsUiState(
    val tvDetails: TVDetails? = null,
    val isInWatchlist: Boolean = false,
    val isLoading: Boolean = false,
    val error: String? = null
)
