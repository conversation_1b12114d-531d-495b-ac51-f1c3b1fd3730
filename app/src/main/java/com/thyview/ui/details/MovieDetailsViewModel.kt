package com.thyview.ui.details

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.thyview.models.search.MovieDetails
import com.thyview.models.search.StreamingService
import com.thyview.services.SearchService
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

/**
 * ViewModel for movie details screen
 */
@HiltViewModel
class MovieDetailsViewModel @Inject constructor(
    private val searchService: SearchService
) : ViewModel() {

    private val _uiState = MutableStateFlow(MovieDetailsUiState())
    val uiState: StateFlow<MovieDetailsUiState> = _uiState.asStateFlow()

    /**
     * Load movie details and related data
     */
    fun loadMovieDetails(movieId: Int) {
        viewModelScope.launch {
            _uiState.update { it.copy(isLoading = true, error = null) }
            
            try {
                // Load movie details
                val movieResult = searchService.getMovieDetails(movieId)
                
                movieResult.fold(
                    onSuccess = { movieDetails ->
                        _uiState.update { 
                            it.copy(
                                movieDetails = movieDetails,
                                isLoading = false,
                                error = null
                            ) 
                        }
                        
                        // Load additional data in parallel
                        loadWatchlistStatus(movieId)
                        loadStreamingServices(movieId)
                        
                        Timber.d("Movie details loaded successfully for ID: $movieId")
                    },
                    onFailure = { exception ->
                        _uiState.update { 
                            it.copy(
                                isLoading = false,
                                error = exception.message ?: "Failed to load movie details"
                            ) 
                        }
                        
                        Timber.e(exception, "Failed to load movie details for ID: $movieId")
                    }
                )
            } catch (e: Exception) {
                _uiState.update { 
                    it.copy(
                        isLoading = false,
                        error = e.message ?: "Unexpected error occurred"
                    ) 
                }
                
                Timber.e(e, "Unexpected error loading movie details for ID: $movieId")
            }
        }
    }

    /**
     * Toggle watchlist status
     */
    fun toggleWatchlist() {
        val currentState = _uiState.value
        val movieDetails = currentState.movieDetails ?: return
        
        viewModelScope.launch {
            try {
                val result = searchService.toggleWatchlist(
                    contentId = movieDetails.id,
                    contentType = "movie",
                    isCurrentlyInWatchlist = currentState.isInWatchlist
                )
                
                result.fold(
                    onSuccess = { response ->
                        _uiState.update { 
                            it.copy(isInWatchlist = response.isInWatchlist) 
                        }
                        
                        Timber.d("Watchlist toggled successfully for movie ID: ${movieDetails.id}")
                    },
                    onFailure = { exception ->
                        Timber.e(exception, "Failed to toggle watchlist for movie ID: ${movieDetails.id}")
                        // Could show a snackbar or toast here
                    }
                )
            } catch (e: Exception) {
                Timber.e(e, "Unexpected error toggling watchlist for movie ID: ${movieDetails.id}")
            }
        }
    }

    /**
     * Load watchlist status
     */
    private fun loadWatchlistStatus(movieId: Int) {
        viewModelScope.launch {
            try {
                val result = searchService.isInWatchlist("movie", movieId)
                
                result.fold(
                    onSuccess = { isInWatchlist ->
                        _uiState.update { it.copy(isInWatchlist = isInWatchlist) }
                        Timber.d("Watchlist status loaded for movie ID: $movieId")
                    },
                    onFailure = { exception ->
                        Timber.e(exception, "Failed to load watchlist status for movie ID: $movieId")
                    }
                )
            } catch (e: Exception) {
                Timber.e(e, "Unexpected error loading watchlist status for movie ID: $movieId")
            }
        }
    }

    /**
     * Load streaming services
     */
    private fun loadStreamingServices(movieId: Int) {
        viewModelScope.launch {
            try {
                val result = searchService.getStreamingServices("movie", movieId)
                
                result.fold(
                    onSuccess = { services ->
                        _uiState.update { it.copy(streamingServices = services) }
                        Timber.d("Streaming services loaded for movie ID: $movieId")
                    },
                    onFailure = { exception ->
                        Timber.e(exception, "Failed to load streaming services for movie ID: $movieId")
                    }
                )
            } catch (e: Exception) {
                Timber.e(e, "Unexpected error loading streaming services for movie ID: $movieId")
            }
        }
    }
}

/**
 * UI state for movie details screen
 */
data class MovieDetailsUiState(
    val movieDetails: MovieDetails? = null,
    val isInWatchlist: Boolean = false,
    val streamingServices: List<StreamingService> = emptyList(),
    val isLoading: Boolean = false,
    val error: String? = null
)
