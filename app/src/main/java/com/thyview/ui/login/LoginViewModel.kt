package com.thyview.ui.login

import android.content.Context
import android.content.Intent
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.facebook.AccessToken
import com.google.android.gms.auth.api.identity.Identity
import com.google.android.gms.auth.api.identity.SignInClient
import com.google.android.gms.common.api.ApiException
import com.google.android.gms.common.api.CommonStatusCodes
import com.google.firebase.auth.AuthCredential
import com.google.firebase.auth.FacebookAuthProvider
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.auth.GoogleAuthProvider
import com.google.firebase.auth.ktx.auth
import com.google.firebase.ktx.Firebase
import com.thyview.analytics.ANALYTICS_EVENT_SIGN_IN
import com.thyview.analytics.ANALYTICS_EVENT_SIGN_IN_FAILURE
import com.thyview.analytics.ERROR_REASON
import com.thyview.analytics.FirebaseAnalyticsTrackingService
import com.thyview.analytics.MixPanelAnalyticsTrackingService
import com.thyview.models.UserObject
import com.thyview.storage.prefs.AccountPreferences
import com.thyview.utils.Constants
import kotlinx.coroutines.launch
import kotlinx.coroutines.tasks.await
import timber.log.Timber
import java.util.UUID

/**
 * ViewModel for handling login business logic
 */
class LoginViewModel : ViewModel() {
    private val auth: FirebaseAuth = Firebase.auth
    
    /**
     * Handles the Facebook access token and performs authentication with Firebase
     */
    fun handleFacebookAccessToken(token: AccessToken, onSuccess: () -> Unit) {
        val credential = FacebookAuthProvider.getCredential(token.token)
        firebaseAuthWithCredential(credential, SIGN_IN_FACEBOOK, onSuccess)
    }
    
    /**
     * Handles the Google sign-in result
     */
    fun handleGoogleSignInResult(
        context: Context,
        data: Intent?,
        onSuccess: () -> Unit,
        onError: () -> Unit
    ) {
        try {
            val credential = getGoogleCredentialFromIntent(context, data)
            if (credential != null) {
                firebaseAuthWithCredential(credential, SIGN_IN_GOOGLE, onSuccess)
            } else {
                Timber.e("Google Sign In failed: credential is null")
                onError()
            }
        } catch (e: Exception) {
            Timber.e(e, "Google Sign In failed")
            onError()
        }
    }
    
    /**
     * Get Google credential from sign-in intent
     */
    private fun getGoogleCredentialFromIntent(context: Context, data: Intent?): AuthCredential? {
        return try {
            val signInClient: SignInClient = Identity.getSignInClient(context)
            val credential = signInClient.getSignInCredentialFromIntent(data)
            val idToken = credential.googleIdToken
            
            if (idToken != null) {
                GoogleAuthProvider.getCredential(idToken, null)
            } else {
                Timber.e("Google Sign In failed: No ID token!")
                null
            }
        } catch (e: ApiException) {
            when (e.statusCode) {
                CommonStatusCodes.CANCELED -> {
                    Timber.d("Google Sign In canceled")
                }
                CommonStatusCodes.NETWORK_ERROR -> {
                    Timber.e("Google Sign In network error")
                }
                else -> {
                    Timber.e(e, "Google Sign In API exception")
                }
            }
            null
        } catch (e: Exception) {
            Timber.e(e, "Google Sign In exception")
            null
        }
    }
    
    /**
     * Authenticate with Firebase using the provided credential
     */
    private fun firebaseAuthWithCredential(
        credential: AuthCredential,
        signInMethod: String,
        onSuccess: () -> Unit
    ) {
        viewModelScope.launch {
            try {
                val authResult = auth.signInWithCredential(credential).await()
                val user = authResult.user
                
                if (user != null) {
                    handleSuccessfulLogin(user.uid, signInMethod)
                    onSuccess()
                } else {
                    Timber.e("Firebase auth failed: User is null")
                    logAuthFailure(signInMethod, "User is null")
                }
            } catch (e: Exception) {
                Timber.e(e, "Firebase auth failed")
                logAuthFailure(signInMethod, e.message ?: "Unknown error")
            }
        }
    }
    
    /**
     * Handle successful login by storing user data and logging analytics
     */
    private fun handleSuccessfulLogin(userId: String, signInMethod: String) {
        // Set user ID for tracking and analytics
        UserObject.userUID = userId
        
        // Log success events
        FirebaseAnalyticsTrackingService.logEvent(
            context = null,
            name = ANALYTICS_EVENT_SIGN_IN,
            bundle = null
        )
        MixPanelAnalyticsTrackingService.logEvent(
            context = null,
            name = ANALYTICS_EVENT_SIGN_IN,
            map = hashMapOf(MP_LOGIN_METHOD to signInMethod)
        )
        
        // Generate and store unique ID for analytics if not already present
        AccountPreferences.getInstance(null).apply {
            if (getStringValue(Constants.externalId, "").isEmpty()) {
                val externalId = UUID.randomUUID().toString()
                //putStringValue(Constants.externalId, externalId)
                
                // Set user ID for analytics services
                FirebaseAnalyticsTrackingService.setUserId(null, externalId)
                MixPanelAnalyticsTrackingService.setUserId(null, externalId)
            }
        }
    }
    
    /**
     * Log authentication failure to analytics
     */
    private fun logAuthFailure(signInMethod: String, errorReason: String) {
        FirebaseAnalyticsTrackingService.logEvent(
            context = null,
            name = ANALYTICS_EVENT_SIGN_IN_FAILURE,
            bundle = null
        )
        MixPanelAnalyticsTrackingService.logEvent(
            context = null,
            name = ANALYTICS_EVENT_SIGN_IN_FAILURE, 
            map = hashMapOf(
                MP_LOGIN_METHOD to signInMethod,
                ERROR_REASON to errorReason
            )
        )
    }
    
    companion object {
        private const val SIGN_IN_GOOGLE = "google"
        private const val SIGN_IN_FACEBOOK = "facebook"
        private const val MP_LOGIN_METHOD = "login_method"
    }
}