
package com.thyview.ui.login

import android.content.Context
import android.content.DialogInterface
import android.content.Intent
import android.content.IntentSender
import androidx.activity.compose.ManagedActivityResultLauncher
import androidx.activity.compose.rememberLauncherForActivityResult
import androidx.activity.result.ActivityResult
import androidx.activity.result.IntentSenderRequest
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.SpanStyle
import androidx.compose.ui.text.buildAnnotatedString
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.text.style.TextDecoration
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.text.withStyle
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.compose.ui.viewinterop.AndroidView
import com.facebook.CallbackManager
import com.facebook.FacebookCallback
import com.facebook.FacebookException
import com.facebook.login.LoginResult
import com.facebook.login.widget.LoginButton
import com.google.android.gms.auth.api.identity.BeginSignInRequest
import com.google.android.gms.auth.api.identity.GetSignInIntentRequest
import com.google.android.gms.auth.api.identity.Identity
import com.google.android.gms.auth.api.identity.SignInClient
import com.thyview.R
import com.thyview.ui.ThyViewTheme
import com.thyview.utils.AlertDialogView
import timber.log.Timber

/**
 * A pure composable login screen that handles authentication via Google and Facebook
 */
@Composable
fun LoginScreen(
    viewModel: LoginViewModel,
    onNavigateToMain: () -> Unit
) {
    ThyViewTheme {
        val context = LocalContext.current
        val signInClient = remember { Identity.getSignInClient(context) }
        val callbackManager = remember { CallbackManager.Factory.create() }

        var isLoading by remember { mutableStateOf(false) }
        var userCanceledOrDeclinedOneTap by remember { mutableStateOf(false) }

        // Google Sign-in result launcher
        val signInLauncher = rememberLauncherForActivityResult(
            contract = ActivityResultContracts.StartIntentSenderForResult()
        ) { result ->
            handleGoogleSignInResult(
                context = context,
                data = result.data,
                isLoading = { loading -> isLoading = loading },
                viewModel = viewModel,
                onSuccess = onNavigateToMain
            )
        }

        // UI components
        Box(modifier = Modifier.fillMaxSize()) {
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(24.dp),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.Top
            ) {
                // App Title
                AppTitle()

                Spacer(modifier = Modifier.height(40.dp))

                // Add a flexible space at the top
                Spacer(modifier = Modifier.weight(1f))

                // Google sign-in button
                Button(
                    onClick = {
                        if (userCanceledOrDeclinedOneTap) {
                            startGoogleSignInRegular(context, signInClient, signInLauncher)
                        } else {
                            oneTapSignIn(context, signInClient, signInLauncher) { userCanceledOrDeclinedOneTap = true }
                        }
                    },
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(48.dp),
                    colors = ButtonDefaults.buttonColors(
                        containerColor = Color.White
                    )
                ) {
                    Row(
                        verticalAlignment = Alignment.CenterVertically,
                        horizontalArrangement = Arrangement.Start,
                        modifier = Modifier.fillMaxWidth()
                    ) {
                        Image(
                            painter = painterResource(id = R.drawable.google_icon_logo),
                            contentDescription = "Google Sign In",
                            modifier = Modifier.size(24.dp)
                        )
                        Text(
                            text = stringResource(id = R.string.continue_gmail_text),
                            color = Color.Black,
                            textAlign = TextAlign.Center,
                            modifier = Modifier.weight(1f)
                        )
                    }
                }

                Spacer(modifier = Modifier.height(16.dp))

                // Facebook button
                AndroidView(
                    modifier = Modifier
                        .fillMaxWidth()
                        .height(48.dp),
                    factory = { ctx ->
                        LoginButton(ctx).apply {
                            // Force button dimensions to match Google button (48.dp height)
                            layoutParams = android.view.ViewGroup.LayoutParams(
                                android.view.ViewGroup.LayoutParams.MATCH_PARENT,
                                android.view.ViewGroup.LayoutParams.MATCH_PARENT
                            )
                            minimumHeight = 144 // Force height in pixels (48dp * 3 density)
                            setPadding(36, 36, 36, 36) // Remove default padding
                            background = android.graphics.drawable.GradientDrawable().apply {
                                cornerRadius = 48f // Adjust radius as needed
                                setColor(android.graphics.Color.parseColor("#1877F2")) // Facebook blue
                            }
                            setPermissions(EMAIL, PUBLIC_PROFILE)
                            setupFacebookButton(
                                this,
                                callbackManager,
                                context,
                                viewModel,
                                onNavigateToMain
                            )
                        }
                    }
                )

                Spacer(modifier = Modifier.height(8.dp))

                // Terms and conditions text
                TermsAndConditionsText()

                // Add some padding at the bottom
                Spacer(modifier = Modifier.height(16.dp))
            }

            // Loading indicator
            if (isLoading) {
                CircularProgressIndicator(
                    modifier = Modifier.align(Alignment.Center),
                    color = MaterialTheme.colorScheme.primary
                )
            }
        }
    }
}

@Composable
private fun AppTitle() {
    Image(
        painter = painterResource(id = R.drawable.thyview_logo),
        contentDescription = "ThyView Logo",
        modifier = Modifier.padding(64.dp)
    )
}

@Composable
private fun TermsAndConditionsText() {
    val context = LocalContext.current
    val primaryColor = MaterialTheme.colorScheme.primary

    Text(
        text = buildAnnotatedString {
            append(stringResource(id = R.string.proceed_agreement_text)
            )
        },
        color = Color.White,
        fontSize = 14.sp,
        textAlign = TextAlign.Center,
        modifier = Modifier.fillMaxWidth()
    )
}

/**
 * Preview for the LoginScreen composable
 */
@Preview(showBackground = true, name = "LoginScreen Preview")
@Composable
fun LoginScreenPreview() {
    // Create a mock view model for preview purposes
    val mockViewModel = LoginViewModel()
    
    // Provide the LoginScreen with necessary parameters
    LoginScreen(
        viewModel = mockViewModel,
        onNavigateToMain = {} // Empty lambda for preview
    )
}

// Helper functions
private fun setupFacebookButton(
    loginButton: LoginButton,
    callbackManager: CallbackManager,
    context: Context,
    viewModel: LoginViewModel,
    onSuccess: () -> Unit
) {
    loginButton.registerCallback(
        callbackManager,
        object : FacebookCallback<LoginResult> {
            override fun onSuccess(result: LoginResult) {
                Timber.d("facebook:signin success")
                viewModel.handleFacebookAccessToken(result.accessToken, onSuccess)
            }

            override fun onCancel() {
                Timber.d("facebook:onCancel")
            }

            override fun onError(error: FacebookException) {
                Timber.e(error)
                Timber.e("facebook:onError ${error.stackTrace}", error)
                showLoginError(context)
            }
        },
    )
}

private fun showLoginError(context: Context) {
    AlertDialogView.showAlertDialog(
        context = context as? android.app.Activity,
        title = context.getString(R.string.login_error_desc_generic),
        message = context.getString(R.string.login_error_desc_generic),
        buttonPositiveText = context.getString(R.string.okay),
        buttonNegativeText = null
    ) { dialog, which ->
        if (which == DialogInterface.BUTTON_POSITIVE) {
            dialog.cancel()
        }
    }
}

private fun oneTapSignIn(
    context: Context,
    signInClient: SignInClient,
    signInLauncher: ManagedActivityResultLauncher<IntentSenderRequest, ActivityResult>,
    onFailure: () -> Unit
) {
    // Configure One Tap UI
    val oneTapRequest = BeginSignInRequest.builder()
        .setGoogleIdTokenRequestOptions(
            BeginSignInRequest.GoogleIdTokenRequestOptions.builder()
                .setSupported(true)
                .setServerClientId(context.getString(R.string.default_web_client_id))
                .setFilterByAuthorizedAccounts(false)
                .build(),
        )
        .build()

    // Display the One Tap UI
    signInClient.beginSignIn(oneTapRequest)
        .addOnSuccessListener { result ->
            launchGoogleSignIn(result.pendingIntent, signInLauncher)
        }
        .addOnFailureListener { e ->
            Timber.e(e)
            Timber.e("Google sign in Failed: ${e.stackTrace}", e)
            onFailure()
            startGoogleSignInRegular(context, signInClient, signInLauncher)
        }
}

private fun startGoogleSignInRegular(
    context: Context,
    signInClient: SignInClient,
    signInLauncher: ManagedActivityResultLauncher<IntentSenderRequest, ActivityResult>
) {
    val signInRequest = GetSignInIntentRequest.builder()
        .setServerClientId(context.getString(R.string.default_web_client_id))
        .build()

    signInClient.getSignInIntent(signInRequest)
        .addOnSuccessListener { pendingIntent ->
            launchGoogleSignIn(pendingIntent, signInLauncher)
        }
        .addOnFailureListener { e ->
            Timber.e("Failed to start Google Sign In: ${e.message}")
        }
}

private fun launchGoogleSignIn(
    pendingIntent: android.app.PendingIntent,
    signInLauncher: ManagedActivityResultLauncher<IntentSenderRequest, ActivityResult>
) {
    val intentSenderRequest = IntentSenderRequest.Builder(pendingIntent.intentSender)
        .build()
    signInLauncher.launch(intentSenderRequest)
}

private fun handleGoogleSignInResult(
    context: Context,
    data: Intent?,
    isLoading: (Boolean) -> Unit,
    viewModel: LoginViewModel,
    onSuccess: () -> Unit
) {
    isLoading(true)
    viewModel.handleGoogleSignInResult(context, data, onSuccess) {
        isLoading(false)
        showLoginError(context)
    }
}

private const val EMAIL = "email"
private const val PUBLIC_PROFILE = "public_profile"
