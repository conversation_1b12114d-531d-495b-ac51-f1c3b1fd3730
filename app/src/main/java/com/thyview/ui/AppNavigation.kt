package com.thyview.ui

import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.navigation.NavHostController
import androidx.navigation.NavType
import androidx.navigation.compose.NavHost
import androidx.navigation.compose.composable
import androidx.navigation.navArgument
import androidx.lifecycle.viewmodel.compose.viewModel
import com.thyview.ui.articles.ArticleDetailScreen
import com.thyview.ui.articles.ArticlesScreen
import com.thyview.ui.actor.ActorScreen
import com.thyview.ui.home.HomeScreen
import com.thyview.ui.login.LoginScreen
import com.thyview.ui.login.LoginViewModel
import com.thyview.ui.movie.MovieScreen
import com.thyview.ui.profile.ProfileScreen
import com.thyview.ui.reviews.CommentRepliesScreen
import com.thyview.ui.reviews.PostDetailScreen
import com.thyview.ui.reviews.PostsScreen
import com.thyview.ui.reviews.AddReviewScreen
import com.thyview.ui.search.SearchScreen
import com.thyview.ui.details.MovieDetailsScreen
import com.thyview.ui.details.PersonDetailsScreen
import com.thyview.ui.details.TVDetailsScreen
import com.thyview.ui.streaming.StreamingServicesScreen
import java.net.URLDecoder
import java.net.URLEncoder
import java.nio.charset.StandardCharsets

// Define navigation routes
object NavRoutes {
    const val SPLASH = "splash" // Added SPLASH route
    const val HOME = "home"
    const val ARTICLES = "articles"
    const val SEARCH = "search"
    const val REVIEWS = "reviews"
    const val PROFILE = "profile"
    const val LOGIN = "login"
    const val POST_DETAIL = "post_detail/{postId}"
    const val ACTOR = "actor/{actorId}"
    const val MOVIE = "movie/{movieId}"
    const val COMMENT_REPLIES = "replies/{postId}/{commentId}"
    const val ARTICLE_DETAIL = "article_detail/{articleId}/{title}/{contentUrl}"
    const val MOVIE_DETAILS = "movie_details/{movieId}"
    const val TV_DETAILS = "tv_details/{tvShowId}"
    const val PERSON_DETAILS = "person_details/{personId}"
    const val ADD_REVIEW = "add_review"
    const val STREAMING_SERVICES = "streaming_services/{contentId}/{contentType}"
    
    // List of routes where bottom navigation should be hidden
    val hideBottomNavRoutes = listOf(COMMENT_REPLIES)
    
    // Helper function to check if bottom nav should be hidden for a route
    fun shouldHideBottomNav(route: String?): Boolean {
        if (route == null) return false
        return hideBottomNavRoutes.any { 
            // Check if the current route matches any pattern in hideBottomNavRoutes
            // by stripping off parameters
            route.substringBefore("/") == it.substringBefore("/") ||
            route.contains(it.substringBefore("{"))
        }
    }
    
    // Helper functions to navigate with specific IDs
    fun postDetail(postId: String) = "post_detail/$postId"
    fun actor(actorId: String) = "actor/$actorId"
    fun movie(movieId: String) = "movie/$movieId"
    fun commentReplies(postId: String, commentId: String) = "replies/$postId/$commentId"
    fun articleDetail(articleId: String, title: String, contentUrl: String): String {
        val encodedTitle = URLEncoder.encode(title, StandardCharsets.UTF_8.toString())
        val encodedContentUrl = URLEncoder.encode(contentUrl, StandardCharsets.UTF_8.toString())
        return "article_detail/$articleId/$encodedTitle/$encodedContentUrl"
    }

    fun movieDetails(movieId: Int): String = "movie_details/$movieId"
    fun tvDetails(tvShowId: Int): String = "tv_details/$tvShowId"
    fun personDetails(personId: Int): String = "person_details/$personId"
    fun addReview(movieTitle: String? = null): String {
        return if (movieTitle != null) {
            val encodedTitle = URLEncoder.encode(movieTitle, StandardCharsets.UTF_8.toString())
            "add_review?movie=$encodedTitle"
        } else {
            "add_review"
        }
    }
    fun streamingServices(contentId: Int, contentType: String): String = "streaming_services/$contentId/$contentType"
}

@Composable
fun AppNavigation(
    navController: NavHostController,
    modifier: Modifier = Modifier,
    onRouteChanged: (String) -> Unit = {}
) {
    NavHost(
        navController = navController,
        startDestination = NavRoutes.SPLASH, // Changed to start with SPLASH screen
        modifier = modifier
    ) {
        // Add SplashScreen as the start destination
        composable(NavRoutes.SPLASH) {
            SplashScreen(
                navController = navController,
                viewModel = viewModel()
            )
            
            // Notify about route change
            onRouteChanged(NavRoutes.SPLASH)
        }
        composable(NavRoutes.HOME) {
            HomeScreen(navController = navController)
            
            // Notify about route change
            onRouteChanged(NavRoutes.HOME)
        }
        
        composable(NavRoutes.ARTICLES) {
            ArticlesScreen(navController = navController)
            
            // Notify about route change
            onRouteChanged(NavRoutes.ARTICLES)
        }
        
        composable(NavRoutes.SEARCH) {
            SearchScreen(navController = navController)
            
            // Notify about route change
            onRouteChanged(NavRoutes.SEARCH)
        }
        
        composable(NavRoutes.REVIEWS) {
            PostsScreen(navController = navController)
            
            // Notify about route change
            onRouteChanged(NavRoutes.REVIEWS)
        }
        
        composable(NavRoutes.PROFILE) {
            ProfileScreen(navController = navController)
            
            // Notify about route change
            onRouteChanged(NavRoutes.PROFILE)
        }
        
        // Added LoginScreen to navigation
        composable(NavRoutes.LOGIN) {
            val loginViewModel: LoginViewModel = viewModel()
            LoginScreen(
                viewModel = loginViewModel,
                onNavigateToMain = {
                    navController.navigate(NavRoutes.HOME) {
                        popUpTo(NavRoutes.SPLASH) { // Updated to clear back to splash
                            inclusive = true
                        }
                        launchSingleTop = true
                    }
                }
            )
            
            // Notify about route change
            onRouteChanged(NavRoutes.LOGIN)
        }
        
        composable(
            route = NavRoutes.POST_DETAIL,
            arguments = listOf(navArgument("postId") { type = NavType.StringType })
        ) { backStackEntry ->
            val postId = backStackEntry.arguments?.getString("postId") ?: ""
            PostDetailScreen(
                postId = postId,
                navController = navController
            )
            
            // Notify about route change
            onRouteChanged(NavRoutes.POST_DETAIL)
        }
        
        // New screen for Actor details
        composable(
            route = NavRoutes.ACTOR,
            arguments = listOf(navArgument("actorId") { type = NavType.StringType })
        ) { backStackEntry ->
            val actorId = backStackEntry.arguments?.getString("actorId") ?: ""
            ActorScreen(
                actorId = actorId,
                navController = navController
            )
            
            // Notify about route change
            onRouteChanged(NavRoutes.ACTOR)
        }
        
        // New screen for Movie details
        composable(
            route = NavRoutes.MOVIE,
            arguments = listOf(navArgument("movieId") { type = NavType.StringType })
        ) { backStackEntry ->
            val movieId = backStackEntry.arguments?.getString("movieId") ?: ""
            MovieScreen(
                movieId = movieId,
                navController = navController
            )
            
            // Notify about route change
            onRouteChanged(NavRoutes.MOVIE)
        }
        
        // Screen for Comment Replies
        composable(
            route = NavRoutes.COMMENT_REPLIES,
            arguments = listOf(
                navArgument("postId") { type = NavType.StringType },
                navArgument("commentId") { type = NavType.StringType }
            )
        ) { backStackEntry ->
            val postId = backStackEntry.arguments?.getString("postId") ?: ""
            val commentId = backStackEntry.arguments?.getString("commentId") ?: ""
            CommentRepliesScreen(
                postId = postId,
                commentId = commentId,
                navController = navController
            )
            
            // Notify about route change with the complete route to ensure bottom nav is hidden
            onRouteChanged(NavRoutes.COMMENT_REPLIES)
        }
        
        // New screen for Article Detail
        composable(
            route = NavRoutes.ARTICLE_DETAIL,
            arguments = listOf(
                navArgument("articleId") { type = NavType.StringType },
                navArgument("title") { type = NavType.StringType },
                navArgument("contentUrl") { type = NavType.StringType }
            )
        ) { backStackEntry ->
            val articleId = backStackEntry.arguments?.getString("articleId") ?: ""
            val encodedTitle = backStackEntry.arguments?.getString("title") ?: ""
            val encodedContentUrl = backStackEntry.arguments?.getString("contentUrl") ?: ""
            
            val decodedTitle = URLDecoder.decode(encodedTitle, StandardCharsets.UTF_8.toString())
            val decodedContentUrl = URLDecoder.decode(encodedContentUrl, StandardCharsets.UTF_8.toString())
            
            ArticleDetailScreen(
                navController = navController,
                articleId = articleId,
                title = decodedTitle,
                contentUrl = decodedContentUrl
            )
            
            // Notify about route change
            onRouteChanged(NavRoutes.ARTICLE_DETAIL)
        }

        // Movie Details Screen
        composable(
            route = NavRoutes.MOVIE_DETAILS,
            arguments = listOf(navArgument("movieId") { type = NavType.IntType })
        ) { backStackEntry ->
            val movieId = backStackEntry.arguments?.getInt("movieId") ?: 0
            MovieDetailsScreen(
                movieId = movieId,
                navController = navController
            )
            onRouteChanged(NavRoutes.MOVIE_DETAILS)
        }

        // TV Details Screen
        composable(
            route = NavRoutes.TV_DETAILS,
            arguments = listOf(navArgument("tvShowId") { type = NavType.IntType })
        ) { backStackEntry ->
            val tvShowId = backStackEntry.arguments?.getInt("tvShowId") ?: 0
            TVDetailsScreen(
                tvShowId = tvShowId,
                navController = navController
            )
            onRouteChanged(NavRoutes.TV_DETAILS)
        }

        // Person Details Screen
        composable(
            route = NavRoutes.PERSON_DETAILS,
            arguments = listOf(navArgument("personId") { type = NavType.IntType })
        ) { backStackEntry ->
            val personId = backStackEntry.arguments?.getInt("personId") ?: 0
            PersonDetailsScreen(
                personId = personId,
                navController = navController
            )
            onRouteChanged(NavRoutes.PERSON_DETAILS)
        }

        // Add Review Screen
        composable(
            route = "${NavRoutes.ADD_REVIEW}?movie={movie}",
            arguments = listOf(navArgument("movie") {
                type = NavType.StringType
                nullable = true
                defaultValue = null
            })
        ) { backStackEntry ->
            val movieTitle = backStackEntry.arguments?.getString("movie")?.let {
                URLDecoder.decode(it, StandardCharsets.UTF_8.toString())
            }
            AddReviewScreen(
                movieTitle = movieTitle,
                navController = navController
            )
            onRouteChanged(NavRoutes.ADD_REVIEW)
        }

        // Streaming Services Screen
        composable(
            route = NavRoutes.STREAMING_SERVICES,
            arguments = listOf(
                navArgument("contentId") { type = NavType.IntType },
                navArgument("contentType") { type = NavType.StringType }
            )
        ) { backStackEntry ->
            val contentId = backStackEntry.arguments?.getInt("contentId") ?: 0
            val contentType = backStackEntry.arguments?.getString("contentType") ?: ""
            StreamingServicesScreen(
                contentId = contentId,
                contentType = contentType,
                navController = navController
            )
            onRouteChanged(NavRoutes.STREAMING_SERVICES)
        }
    }
}
