package com.thyview.ui.streaming

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.thyview.models.search.StreamingService
import com.thyview.services.SearchService
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.*
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

/**
 * ViewModel for streaming services screen
 */
@HiltViewModel
class StreamingServicesViewModel @Inject constructor(
    private val searchService: SearchService
) : ViewModel() {

    private val _uiState = MutableStateFlow(StreamingServicesUiState())
    val uiState: StateFlow<StreamingServicesUiState> = _uiState.asStateFlow()

    /**
     * Load streaming services for content
     */
    fun loadStreamingServices(contentId: Int, contentType: String) {
        viewModelScope.launch {
            _uiState.update { it.copy(isLoading = true, error = null) }
            
            try {
                val result = searchService.getStreamingServices(contentType, contentId)
                
                result.fold(
                    onSuccess = { services ->
                        _uiState.update { 
                            it.copy(
                                streamingServices = services,
                                isLoading = false,
                                error = null
                            ) 
                        }
                        
                        Timber.d("Streaming services loaded successfully for $contentType ID: $contentId")
                    },
                    onFailure = { exception ->
                        _uiState.update { 
                            it.copy(
                                isLoading = false,
                                error = exception.message ?: "Failed to load streaming services"
                            ) 
                        }
                        
                        Timber.e(exception, "Failed to load streaming services for $contentType ID: $contentId")
                    }
                )
            } catch (e: Exception) {
                _uiState.update { 
                    it.copy(
                        isLoading = false,
                        error = e.message ?: "Unexpected error occurred"
                    ) 
                }
                
                Timber.e(e, "Unexpected error loading streaming services for $contentType ID: $contentId")
            }
        }
    }
}

/**
 * UI state for streaming services screen
 */
data class StreamingServicesUiState(
    val streamingServices: List<StreamingService> = emptyList(),
    val isLoading: Boolean = false,
    val error: String? = null
)
