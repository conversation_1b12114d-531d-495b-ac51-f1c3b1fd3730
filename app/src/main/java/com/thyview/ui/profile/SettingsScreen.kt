package com.thyview.ui.profile

import android.content.Context
import android.content.Intent
import android.net.Uri
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.navigation.NavController
import androidx.navigation.compose.rememberNavController
import com.google.firebase.auth.ktx.auth
import com.google.firebase.ktx.Firebase
import com.thyview.R
import com.thyview.services.AuthService
import com.thyview.ui.ImdbBlack
import com.thyview.ui.ImdbWhite
import com.thyview.ui.ImdbYellow
import com.thyview.ui.NavRoutes
import com.thyview.ui.ThyViewTheme

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SettingsScreen(
    navController: NavController,
    isUserAuthenticated: Boolean = true
) {
    val context = LocalContext.current
    var showDeleteDialog by remember { mutableStateOf(false) }

    ThyViewTheme {
        Scaffold(
            topBar = {
                TopAppBar(
                    title = { 
                        Text(
                            text = stringResource(R.string.settings),
                            color = ImdbYellow
                        ) 
                    },
                    navigationIcon = {
                        IconButton(onClick = { navController.popBackStack() }) {
                            Icon(
                                imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                                contentDescription = "Back",
                                tint = ImdbYellow
                            )
                        }
                    },
                    colors = TopAppBarDefaults.topAppBarColors(
                        containerColor = ImdbBlack,
                        titleContentColor = ImdbYellow
                    )
                )
            },
            containerColor = ImdbBlack
        ) { innerPadding ->
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(innerPadding)
                    .background(ImdbBlack)
            ) {
                // Privacy Policy
                SettingsItem(
                    title = stringResource(R.string.privacy_policy_title),
                    onClick = {
                        openExternalLink(context, "https://www.LoveBeats.co/privacy-policy")
                    }
                )
                
                Divider(color = ImdbWhite.copy(alpha = 0.1f), thickness = 1.dp)
                
                // Terms & Conditions
                SettingsItem(
                    title = stringResource(R.string.terms_conditions_title),
                    onClick = {
                        openExternalLink(context, "https://www.LoveBeats.co/terms-and-conditions")
                    }
                )
                
                // Show logout and delete account only for authenticated users
                if (isUserAuthenticated) {
                    Divider(color = ImdbWhite.copy(alpha = 0.1f), thickness = 1.dp)
                    
                    // Logout
                    SettingsItem(
                        title = stringResource(R.string.logout),
                        onClick = {
                            handleLogout(context, navController)
                        }
                    )
                    
                    Divider(color = ImdbWhite.copy(alpha = 0.1f), thickness = 1.dp)
                    
                    // Delete Account (always last)
                    SettingsItem(
                        title = stringResource(R.string.delete_account),
                        onClick = {
                            showDeleteDialog = true
                        }
                    )
                }
            }
        }
    }

    // Delete Account Confirmation Dialog
    if (showDeleteDialog) {
        AlertDialog(
            onDismissRequest = { showDeleteDialog = false },
            title = {
                Text(
                    text = stringResource(R.string.delete_account),
                    color = ImdbWhite
                )
            },
            text = {
                Text(
                    text = stringResource(R.string.delete_account_confirmation),
                    color = ImdbWhite
                )
            },
            confirmButton = {
                TextButton(
                    onClick = {
                        showDeleteDialog = false
                        handleDeleteAccount(context, navController)
                    }
                ) {
                    Text(
                        text = stringResource(R.string.delete),
                        color = ImdbYellow
                    )
                }
            },
            dismissButton = {
                TextButton(
                    onClick = { showDeleteDialog = false }
                ) {
                    Text(
                        text = stringResource(R.string.cancel),
                        color = ImdbWhite
                    )
                }
            },
            containerColor = ImdbBlack,
            titleContentColor = ImdbWhite,
            textContentColor = ImdbWhite
        )
    }
}

@Composable
private fun SettingsItem(
    title: String,
    onClick: () -> Unit
) {
    Row(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onClick() }
            .padding(horizontal = 16.dp, vertical = 16.dp),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Text(
            text = title,
            color = ImdbWhite,
            style = MaterialTheme.typography.bodyLarge,
            fontWeight = FontWeight.Medium,
            modifier = Modifier.weight(1f)
        )
    }
}

private fun openExternalLink(context: Context, url: String) {
    val intent = Intent(Intent.ACTION_VIEW, Uri.parse(url))
    context.startActivity(intent)
}

private fun handleLogout(context: Context, navController: NavController) {
    // Sign out from Firebase
    Firebase.auth.signOut()
    
    // Clean up app data
    AuthService.cleanup(context)
    
    // Navigate to login screen
    navController.navigate(NavRoutes.LOGIN) {
        popUpTo(navController.graph.startDestinationId) {
            inclusive = true
        }
        launchSingleTop = true
    }
}

private fun handleDeleteAccount(context: Context, navController: NavController) {
    val user = Firebase.auth.currentUser
    user?.delete()?.addOnCompleteListener { task ->
        if (task.isSuccessful) {
            // Clean up app data
            AuthService.cleanup(context)
            
            // Navigate to login screen
            navController.navigate(NavRoutes.LOGIN) {
                popUpTo(navController.graph.startDestinationId) {
                    inclusive = true
                }
                launchSingleTop = true
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
fun SettingsScreenPreview() {
    val mockNavController = rememberNavController()
    SettingsScreen(
        navController = mockNavController,
        isUserAuthenticated = true
    )
}

@Preview(showBackground = true)
@Composable
fun SettingsScreenGuestPreview() {
    val mockNavController = rememberNavController()
    SettingsScreen(
        navController = mockNavController,
        isUserAuthenticated = false
    )
}
