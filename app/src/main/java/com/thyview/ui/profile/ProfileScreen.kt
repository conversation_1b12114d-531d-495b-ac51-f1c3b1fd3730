package com.thyview.ui.profile

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Person
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.navigation.NavController
import androidx.navigation.compose.rememberNavController
import com.google.firebase.auth.ktx.auth
import com.google.firebase.ktx.Firebase
import com.thyview.BuildConfig
import com.thyview.R
import com.thyview.services.AuthService
import com.thyview.ui.ImdbBlack
import com.thyview.ui.ImdbDarkGray
import com.thyview.ui.ImdbWhite
import com.thyview.ui.ImdbYellow
import com.thyview.ui.NavRoutes
import com.thyview.ui.ThyViewTheme

@Composable
fun ProfileScreen(navController: NavController) {
    val isUserAuthenticated = AuthService.isUserAuthenticated()

    ThyViewTheme {
        if (isUserAuthenticated) {
            UserProfileView(navController = navController)
        } else {
            GuestProfileView(navController = navController)
        }
    }
}


@Composable
private fun GuestProfileView(navController: NavController) {
    var showWatchlistDialog by remember { mutableStateOf(false) }
    var showPreferencesDialog by remember { mutableStateOf(false) }

    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(ImdbBlack)
            .verticalScroll(rememberScrollState())
            .padding(16.dp)
    ) {
        // Top Section - Avatar and Welcome Message
        Column(
            modifier = Modifier.fillMaxWidth(),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            // Default Avatar
            Box(
                modifier = Modifier
                    .size(80.dp)
                    .clip(CircleShape)
                    .background(ImdbDarkGray),
                contentAlignment = Alignment.Center
            ) {
                Icon(
                    imageVector = Icons.Default.Person,
                    contentDescription = "Profile Avatar",
                    tint = ImdbWhite,
                    modifier = Modifier.size(40.dp)
                )
            }

            Spacer(modifier = Modifier.height(16.dp))

            // Welcome Message
            Text(
                text = stringResource(R.string.welcome_guest_message),
                color = ImdbWhite,
                style = MaterialTheme.typography.bodyLarge,
                textAlign = TextAlign.Center,
                modifier = Modifier.padding(horizontal = 16.dp)
            )

            Spacer(modifier = Modifier.height(24.dp))

            // Sign In/Sign Up Button
            Button(
                onClick = { navController.navigate(NavRoutes.LOGIN) },
                colors = ButtonDefaults.buttonColors(
                    containerColor = ImdbYellow,
                    contentColor = ImdbBlack
                ),
                modifier = Modifier.fillMaxWidth()
            ) {
                Text(
                    text = stringResource(R.string.sign_in_sign_up),
                    fontWeight = FontWeight.Medium
                )
            }
        }

        Spacer(modifier = Modifier.height(32.dp))

        // Stats Block
        StatsBlock(
            watchlistCount = 0,
            reviewsCount = 0,
            fannedCount = 0,
            averageRating = "—",
            onWatchlistClick = { showWatchlistDialog = true },
            onReviewsClick = { /* Placeholder for future implementation */ },
            onFannedClick = { /* Placeholder for future implementation */ },
            onAverageRatingClick = { /* Placeholder for future implementation */ }
        )

        Spacer(modifier = Modifier.height(32.dp))

        // Preferences Section
        SectionHeader(title = "Account")
        ProfileMenuItem(
            title = stringResource(R.string.preferences),
            onClick = { showPreferencesDialog = true }
        )

        Spacer(modifier = Modifier.height(24.dp))

        // Settings Section
        SectionHeader(title = "General")
        ProfileMenuItem(
            title = stringResource(R.string.settings),
            onClick = { navController.navigate(NavRoutes.SETTINGS) }
        )

        Spacer(modifier = Modifier.weight(1f))

        // App Version at bottom
        Text(
            text = stringResource(R.string.app_version, BuildConfig.VERSION_NAME),
            color = ImdbWhite.copy(alpha = 0.6f),
            style = MaterialTheme.typography.bodySmall,
            textAlign = TextAlign.Center,
            modifier = Modifier.fillMaxWidth()
        )

        Spacer(modifier = Modifier.height(16.dp))
    }

    // Watchlist Dialog
    if (showWatchlistDialog) {
        AlertDialog(
            onDismissRequest = { showWatchlistDialog = false },
            title = {
                Text(
                    text = stringResource(R.string.watchlist),
                    color = ImdbWhite
                )
            },
            text = {
                Text(
                    text = stringResource(R.string.sign_in_required_watchlist),
                    color = ImdbWhite
                )
            },
            confirmButton = {
                TextButton(
                    onClick = {
                        showWatchlistDialog = false
                        navController.navigate(NavRoutes.LOGIN)
                    }
                ) {
                    Text(
                        text = stringResource(R.string.okay),
                        color = ImdbYellow
                    )
                }
            },
            containerColor = ImdbBlack,
            titleContentColor = ImdbWhite,
            textContentColor = ImdbWhite
        )
    }

    // Preferences Dialog
    if (showPreferencesDialog) {
        AlertDialog(
            onDismissRequest = { showPreferencesDialog = false },
            title = {
                Text(
                    text = stringResource(R.string.preferences),
                    color = ImdbWhite
                )
            },
            text = {
                Text(
                    text = stringResource(R.string.sign_in_required_preferences),
                    color = ImdbWhite
                )
            },
            confirmButton = {
                TextButton(
                    onClick = {
                        showPreferencesDialog = false
                        navController.navigate(NavRoutes.LOGIN)
                    }
                ) {
                    Text(
                        text = stringResource(R.string.okay),
                        color = ImdbYellow
                    )
                }
            },
            containerColor = ImdbBlack,
            titleContentColor = ImdbWhite,
            textContentColor = ImdbWhite
        )
    }
}

@Composable
private fun UserProfileView(navController: NavController) {
    val currentUser = Firebase.auth.currentUser

    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(ImdbBlack)
            .verticalScroll(rememberScrollState())
            .padding(16.dp)
    ) {
        // Top Section - User Info
        Column(
            modifier = Modifier.fillMaxWidth(),
            horizontalAlignment = Alignment.CenterHorizontally
        ) {
            // User Avatar
            Box(
                modifier = Modifier
                    .size(80.dp)
                    .clip(CircleShape)
                    .background(ImdbDarkGray),
                contentAlignment = Alignment.Center
            ) {
                Icon(
                    imageVector = Icons.Default.Person,
                    contentDescription = "Profile Avatar",
                    tint = ImdbWhite,
                    modifier = Modifier.size(40.dp)
                )
            }

            Spacer(modifier = Modifier.height(16.dp))

            // User Display Name
            Text(
                text = currentUser?.displayName ?: "User",
                color = ImdbWhite,
                style = MaterialTheme.typography.headlineMedium,
                fontWeight = FontWeight.SemiBold
            )
        }

        Spacer(modifier = Modifier.height(32.dp))

        // Stats Block with real user data (mock for now)
        StatsBlock(
            watchlistCount = 12, // Mock data
            reviewsCount = 8,    // Mock data
            fannedCount = 24,    // Mock data
            averageRating = "4.2", // Mock data
            onWatchlistClick = { /* Navigate to watchlist screen */ },
            onReviewsClick = { /* Navigate to reviews screen */ },
            onFannedClick = { /* Navigate to fanned screen */ },
            onAverageRatingClick = { /* Navigate to ratings screen */ }
        )

        Spacer(modifier = Modifier.height(32.dp))

        // Preferences Section
        SectionHeader(title = "Account")
        ProfileMenuItem(
            title = stringResource(R.string.preferences),
            onClick = { navController.navigate(NavRoutes.PREFERENCES) }
        )

        Spacer(modifier = Modifier.height(24.dp))

        // Settings Section
        SectionHeader(title = "General")
        ProfileMenuItem(
            title = stringResource(R.string.settings),
            onClick = { navController.navigate(NavRoutes.SETTINGS) }
        )

        Spacer(modifier = Modifier.weight(1f))

        // App Version at bottom
        Text(
            text = stringResource(R.string.app_version, BuildConfig.VERSION_NAME),
            color = ImdbWhite.copy(alpha = 0.6f),
            style = MaterialTheme.typography.bodySmall,
            textAlign = TextAlign.Center,
            modifier = Modifier.fillMaxWidth()
        )

        Spacer(modifier = Modifier.height(16.dp))
    }
}

@Composable
private fun StatsBlock(
    watchlistCount: Int,
    reviewsCount: Int,
    fannedCount: Int,
    averageRating: String,
    onWatchlistClick: () -> Unit,
    onReviewsClick: () -> Unit,
    onFannedClick: () -> Unit,
    onAverageRatingClick: () -> Unit
) {
    Card(
        modifier = Modifier.fillMaxWidth(),
        colors = CardDefaults.cardColors(containerColor = ImdbDarkGray),
        shape = RoundedCornerShape(8.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            horizontalArrangement = Arrangement.SpaceEvenly
        ) {
            StatItem(
                label = stringResource(R.string.watchlist),
                value = watchlistCount.toString(),
                onClick = onWatchlistClick,
                modifier = Modifier.weight(1f)
            )
            StatItem(
                label = stringResource(R.string.reviews),
                value = reviewsCount.toString(),
                onClick = onReviewsClick,
                modifier = Modifier.weight(1f)
            )
            StatItem(
                label = stringResource(R.string.fanned),
                value = fannedCount.toString(),
                onClick = onFannedClick,
                modifier = Modifier.weight(1f)
            )
            StatItem(
                label = stringResource(R.string.average_rating),
                value = averageRating,
                onClick = onAverageRatingClick,
                modifier = Modifier.weight(1f)
            )
        }
    }
}

@Composable
private fun StatItem(
    label: String,
    value: String,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Column(
        modifier = modifier
            .clickable { onClick() }
            .padding(8.dp),
        horizontalAlignment = Alignment.CenterHorizontally
    ) {
        Text(
            text = value,
            color = ImdbYellow,
            style = MaterialTheme.typography.headlineMedium,
            fontWeight = FontWeight.Bold
        )
        Text(
            text = label,
            color = ImdbWhite,
            style = MaterialTheme.typography.bodySmall,
            textAlign = TextAlign.Center
        )
    }
}

@Preview(showBackground = true, widthDp = 360)
@Composable
fun ProfileScreenPreview() {
    // Using a mock NavController for the preview
    val mockNavController = rememberNavController()

    ProfileScreen(navController = mockNavController)
}

@Composable
private fun SectionHeader(title: String) {
    Text(
        text = title,
        color = ImdbWhite.copy(alpha = 0.8f),
        style = MaterialTheme.typography.titleMedium,
        fontWeight = FontWeight.SemiBold,
        modifier = Modifier.padding(bottom = 8.dp)
    )
}

@Composable
private fun ProfileMenuItem(
    title: String,
    onClick: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .clickable { onClick() },
        colors = CardDefaults.cardColors(containerColor = ImdbDarkGray),
        shape = RoundedCornerShape(8.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = title,
                color = ImdbWhite,
                style = MaterialTheme.typography.bodyLarge,
                fontWeight = FontWeight.Medium,
                modifier = Modifier.weight(1f)
            )
        }
    }
}

@Preview(showBackground = true, widthDp = 360)
@Composable
fun GuestProfileViewPreview() {
    val mockNavController = rememberNavController()
    ThyViewTheme {
        GuestProfileView(navController = mockNavController)
    }
}

@Preview(showBackground = true, widthDp = 360)
@Composable
fun UserProfileViewPreview() {
    val mockNavController = rememberNavController()
    ThyViewTheme {
        UserProfileView(navController = mockNavController)
    }
}
