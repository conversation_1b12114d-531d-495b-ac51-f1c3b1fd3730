package com.thyview.ui.profile

import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.*
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.navigation.NavController
import androidx.navigation.NavHostController
import androidx.navigation.compose.rememberNavController
import com.thyview.R
import com.thyview.models.UserObject
import com.thyview.ui.NavRoutes

@Composable
fun ProfileScreen(navController: NavController) {
    val context = LocalContext.current
    
    // Check if server onboarding is complete
    if (!UserObject.isServerOnboardingComplete) {
        // Navigate to LoginScreen using NavRoutes
        LaunchedEffect(Unit) {
            (navController as? NavHostController)?.navigate(NavRoutes.LOGIN) {
                // Clear the back stack so user can't go back
                popUpTo(navController.graph.startDestinationId) {
                    inclusive = true
                }
            }
        }
        
        // Show loading UI while navigation is happening
        Box(
            modifier = Modifier.fillMaxSize(),
            contentAlignment = Alignment.Center
        ) {
            CircularProgressIndicator()
        }
    } else {
        // Show current profile UI
        CurrentProfileUI()
    }
    
    // Function to handle logout if needed
    fun handleLogout() {
        (navController as? NavHostController)?.navigate(NavRoutes.LOGIN) {
            // Clear the back stack so user can't go back after logout
            popUpTo(navController.graph.startDestinationId) {
                inclusive = true
            }
        }
    }
}


@Composable
private fun CurrentProfileUI() {
    Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        Text(text = stringResource(R.string.title_profile))
    }
}

@Preview(showBackground = true, widthDp = 360)
@Composable
fun ProfileScreenPreview() {
    // Using a mock NavController for the preview
    val mockNavController = rememberNavController()
    
    ProfileScreen(navController = mockNavController)
}

@Preview(showBackground = true, widthDp = 360)
@Composable
fun LoadingScreenPreview() {
    Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = Alignment.Center
    ) {
        CircularProgressIndicator()
    }
}

@Preview(showBackground = true, widthDp = 360)
@Composable
fun CurrentProfileUIPreview() {
    // Force preview to show the current profile UI
    CurrentProfileUI()
}
