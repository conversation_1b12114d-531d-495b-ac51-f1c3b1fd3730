package com.thyview.di

import com.thyview.services.AWSPostService
import com.thyview.services.IPostService
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Provider for PostService that provides the Firebase implementation.
 */
@Singleton
class PostServiceProvider @Inject constructor(
    private val AWSPostService: AWSPostService
) {
    /**
     * Get the PostService implementation.
     */
    fun getPostService(): IPostService {
        return AWSPostService
    }
}
