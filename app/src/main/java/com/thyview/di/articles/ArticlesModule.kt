package com.thyview.di.articles

import com.thyview.repository.articles.ArticlesRepository
import com.thyview.services.articles.ArticlesApiService
import com.thyview.services.articles.ArticlesApiServiceImpl
import com.thyview.network.service.base.TLSNetworkService
import com.thyview.network.service.base.TLSNetworkService.createRestClient
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import okhttp3.HttpUrl.Companion.toHttpUrl
import javax.inject.Singleton
import javax.inject.Qualifier

/**
 * Qualifier to distinguish between the Retrofit-generated service and the implementation
 */
@Qualifier
@Retention(AnnotationRetention.BINARY)
annotation class RetrofitArticlesApiService

@Module
@InstallIn(SingletonComponent::class)
object ArticlesModule {

    @RetrofitArticlesApiService
    @Provides
    @Singleton
    fun provideRetrofitArticlesApiService(): ArticlesApiService {

        val baseUrl = "https://api.thyview.com".toHttpUrl()
        
        // Create a Configuration object as required by createRestClient
        val configuration = TLSNetworkService.Configuration(
            baseUrl = baseUrl,
            interceptors = emptyList(),
            debugEnabled = false
        )
        
        return createRestClient<ArticlesApiService>(configuration)
    }
    
    @Provides
    @Singleton
    fun provideArticlesApiService(
        @RetrofitArticlesApiService retrofitArticlesApiService: ArticlesApiService
    ): ArticlesApiService {
        return ArticlesApiServiceImpl(retrofitArticlesApiService)
    }

    @Provides
    @Singleton
    fun provideArticlesRepository(articlesApiService: ArticlesApiService): ArticlesRepository {
        return ArticlesRepository(articlesApiService)
    }
}
