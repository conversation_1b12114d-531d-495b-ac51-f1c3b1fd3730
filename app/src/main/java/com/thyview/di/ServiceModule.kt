package com.thyview.di

import android.content.Context
import com.google.gson.Gson
import com.thyview.repository.PostRepository
import com.thyview.services.*
import com.thyview.services.api.CommentApiService
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.android.qualifiers.ApplicationContext
import dagger.hilt.components.SingletonComponent
import javax.inject.Singleton

@Module
@InstallIn(SingletonComponent::class)
object ServiceModule {
    
    @Provides
    @Singleton
    fun provideGson(): Gson {
        return Gson()
    }
    
    @Provides
    @Singleton
    fun providePostServiceProvider(
        awsPostService: AWSPostService
    ): PostServiceProvider {
        return PostServiceProvider(awsPostService)
    }
    
    @Provides
    @Singleton
    fun providePostRepository(
        postServiceProvider: PostServiceProvider
    ): PostRepository {
        return PostRepository(postServiceProvider)
    }
    
    @Provides
    @Singleton
    fun provideImageUploadService(
        postRepository: PostRepository,
        @ApplicationContext context: Context
    ): ImageUploadService {
        return ImageUploadService(postRepository, context)
    }
    
    /**
     * Provides a singleton instance of CommentService
     */
    @Provides
    @Singleton
    fun provideCommentService(
        commentApiService: CommentApiService,
        gson: Gson
    ): CommentService {
        return CommentService(commentApiService, gson)
    }
}
