package com.thyview.di

import com.thyview.BuildConfig
import com.facebook.stetho.okhttp3.StethoInterceptor
import com.thyview.services.api.CommentApiService
import com.thyview.services.PostApiService
import com.thyview.services.SearchApiService
import dagger.Module
import dagger.Provides
import dagger.hilt.InstallIn
import dagger.hilt.components.SingletonComponent
import okhttp3.Interceptor
import okhttp3.OkHttpClient
import okhttp3.logging.HttpLoggingInterceptor
import retrofit2.CallAdapter
import retrofit2.Converter
import retrofit2.Retrofit
import retrofit2.adapter.rxjava3.RxJava3CallAdapterFactory
import retrofit2.converter.gson.GsonConverterFactory
import retrofit2.converter.scalars.ScalarsConverterFactory
import java.util.concurrent.TimeUnit
import javax.inject.Named
import javax.inject.Singleton


@Module
@InstallIn(SingletonComponent::class)
object NetworkModule {

    // Define API base URL here since it's not in BuildConfig
    private const val API_BASE_URL = "https://api.thyview.com/" // Replace with your actual API URL

    private const val CONNECT_TIMEOUT = 60000L
    private const val READ_TIMEOUT = 60000L
    private const val WRITE_TIMEOUT = 60000L

    /**
     * Provides HttpLoggingInterceptor for debugging HTTP requests and responses
     */
    @Provides
    @Singleton
    fun provideHttpLoggingInterceptor(): HttpLoggingInterceptor {
        return HttpLoggingInterceptor().apply {
            level = if (BuildConfig.DEBUG) {
                HttpLoggingInterceptor.Level.BODY
            } else {
                HttpLoggingInterceptor.Level.NONE
            }
        }
    }

    /**
     * Provides StethoInterceptor for network debugging with Chrome DevTools
     */
    @Provides
    @Singleton
    fun provideStethoInterceptor(): StethoInterceptor {
        return StethoInterceptor()
    }

    /**
     * Creates a list of debug interceptors to be added to the OkHttpClient
     * Uses @JvmSuppressWildcards to fix generic type issues with Dagger
     */
    @Provides
    @Singleton
    @Named("debugInterceptors")
    fun provideDebugInterceptors(
        httpLoggingInterceptor: HttpLoggingInterceptor
    ): @JvmSuppressWildcards List<Interceptor> {
        return if (BuildConfig.DEBUG) {
            listOf(httpLoggingInterceptor)
        } else {
            emptyList()
        }
    }

    /**
     * Creates a list of debug network interceptors to be added to the OkHttpClient
     * Uses @JvmSuppressWildcards to fix generic type issues with Dagger
     */
    @Provides
    @Singleton
    @Named("debugNetworkInterceptors")
    fun provideDebugNetworkInterceptors(
        stethoInterceptor: StethoInterceptor
    ): @JvmSuppressWildcards List<Interceptor> {
        return if (BuildConfig.DEBUG) {
            listOf(stethoInterceptor)
        } else {
            emptyList()
        }
    }

    /**
     * Provides a consolidated OkHttpClient instance that can be used throughout the app.
     * Uses @JvmSuppressWildcards on List parameters to fix generic type issues with Dagger
     */
    @Provides
    @Singleton
    fun provideOkHttpClient(
        @Named("debugInterceptors") debugInterceptors: @JvmSuppressWildcards List<Interceptor>,
        @Named("debugNetworkInterceptors") debugNetworkInterceptors: @JvmSuppressWildcards List<Interceptor>
    ): OkHttpClient {
        return OkHttpClient.Builder()
            .connectTimeout(CONNECT_TIMEOUT, TimeUnit.MILLISECONDS)
            .readTimeout(READ_TIMEOUT, TimeUnit.MILLISECONDS)
            .writeTimeout(WRITE_TIMEOUT, TimeUnit.MILLISECONDS)
            .apply {
                // Add debug interceptors if in debug mode
                debugInterceptors.forEach { addInterceptor(it) }
                debugNetworkInterceptors.forEach { addNetworkInterceptor(it) }
            }
            .build()
    }

    /**
     * Provides a list of CallAdapter.Factory instances for Retrofit
     */
    @Provides
    @Singleton
    fun provideCallAdapterFactories(): List<CallAdapter.Factory> {
        return listOf(
            RxJava3CallAdapterFactory.create()
        )
    }

    /**
     * Provides a list of Converter.Factory instances for Retrofit
     */
    @Provides
    @Singleton
    fun provideConverterFactories(): List<Converter.Factory> {
        return listOf(
            ScalarsConverterFactory.create(),
            GsonConverterFactory.create()
        )
    }

    /**
     * Provides a Retrofit instance configured with the OkHttpClient and converter factories
     */
    @Provides
    @Singleton
    fun provideRetrofit(
        okHttpClient: OkHttpClient,
        converterFactories: @JvmSuppressWildcards List<Converter.Factory>,
        callAdapterFactories: @JvmSuppressWildcards List<CallAdapter.Factory>
    ): Retrofit {
        val builder = Retrofit.Builder()
            .baseUrl(API_BASE_URL)
            .client(okHttpClient)
        
        // Add all converter factories
        converterFactories.forEach { builder.addConverterFactory(it) }
        
        // Add all call adapter factories
        callAdapterFactories.forEach { builder.addCallAdapterFactory(it) }
        
        return builder.build()
    }

    /**
     * Provides PostApiService instance
     */
    @Provides
    @Singleton
    fun providePostApiService(retrofit: Retrofit): PostApiService {
        return retrofit.create(PostApiService::class.java)
    }

    /**
     * Provides CommentApiService instance
     */
    @Provides
    @Singleton
    fun provideCommentApiService(retrofit: Retrofit): CommentApiService {
        return retrofit.create(CommentApiService::class.java)
    }

    /**
     * Provides SearchApiService instance
     */
    @Provides
    @Singleton
    fun provideSearchApiService(retrofit: Retrofit): SearchApiService {
        return retrofit.create(SearchApiService::class.java)
    }
}
