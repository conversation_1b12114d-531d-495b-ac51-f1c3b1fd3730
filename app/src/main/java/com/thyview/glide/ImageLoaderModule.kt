package com.thyview.glide

import android.app.Activity
import android.content.Context
import android.widget.ImageView
import com.bumptech.glide.request.RequestOptions
import java.io.File

object ImageLoaderModule {

    fun loadImageIntoImageViewWithLocalPath(context: Context, localImagePath: String, resultImageView: ImageView) {
        GlideApp.with(context)
                .load(localImagePath)
                .apply(RequestOptions().centerCrop())
                .into(resultImageView)
    }

    fun loadImageIntoImageViewWithFile(context: Context, file: File, resultImageView: ImageView) {
        GlideApp.with(context)
                .load(file)
                .apply(RequestOptions().centerCrop())
                .into(resultImageView)
    }

    private fun isValidContextForGlide(context: Context): Boolean {
        if (context is Activity) {
            if (context.isDestroyed || context.isFinishing) {
                return false
            }
        }
        return true
    }
}