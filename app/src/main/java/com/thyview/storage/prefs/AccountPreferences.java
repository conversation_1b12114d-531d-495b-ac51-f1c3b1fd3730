package com.thyview.storage.prefs;

import android.content.Context;
import android.content.SharedPreferences;

import java.util.Set;

public class AccountPreferences {

    private static AccountPreferences mAccountPrefs;

    private final SharedPreferences mSharedPreferences;
    private final SharedPreferences.Editor mSharedPreferencesEditor;

    public AccountPreferences(Context context) {
        mSharedPreferences = context.getSharedPreferences("thyview_account_prefs", Context.MODE_PRIVATE);
        mSharedPreferencesEditor = mSharedPreferences.edit();
    }

    public static synchronized AccountPreferences getInstance(Context context) {

        if (mAccountPrefs == null) {
            mAccountPrefs = new AccountPreferences(context);
        }
        return mAccountPrefs;
    }

    public void setValue(String key, String value) {
        mSharedPreferencesEditor.putString(key, value);
        mSharedPreferencesEditor.commit();
    }

    public void setValue(String key, Boolean value) {
        mSharedPreferencesEditor.putBoolean(key, value);
        mSharedPreferencesEditor.commit();
    }

    public void setValue(String key, Long value) {
        mSharedPreferencesEditor.putFloat(key, value);
        mSharedPreferencesEditor.commit();
    }

    public void setValue(String key, Set<String> value) {
        mSharedPreferencesEditor.putStringSet(key, value);
        mSharedPreferencesEditor.commit();
    }

    public void setValue(String key, Double value) {
        mSharedPreferencesEditor.putLong(key, Double.doubleToRawLongBits(value));
        mSharedPreferencesEditor.commit();
    }

    public void setValue(String key, Integer value) {
        mSharedPreferencesEditor.putInt(key, value);
        mSharedPreferencesEditor.commit();
    }

    public String getStringValue(String key, String defaultValue) {
        return mSharedPreferences.getString(key, defaultValue);
    }

    public Set<String> getStringSetValue(String key, Set<String> defaultValue) {
        return mSharedPreferences.getStringSet(key, defaultValue);
    }


    public float getFloatValue(String key, Long defaultValue) {
        return mSharedPreferences.getFloat(key, defaultValue);
    }

    public Boolean getBooleanValue(String key, Boolean defaultValue) {
        return mSharedPreferences.getBoolean(key, defaultValue);
    }

    public Double getDoubleValue(String key, Double defaultValue) {
        return Double.longBitsToDouble(mSharedPreferences.getLong(key, Double.doubleToLongBits(defaultValue)));
    }

    public int getIntValue(String key, Integer defaultValue) {
        return mSharedPreferences.getInt(key, defaultValue);
    }

    public void clear() {
        mSharedPreferencesEditor.clear().commit();
    }
}


