package com.thyview.analytics

import android.content.Context
import android.os.Bundle
import com.google.firebase.analytics.FirebaseAnalytics

// https://firebase.google.com/docs/reference/android/com/google/firebase/analytics/FirebaseAnalytics.Event
// Custom events
const val APP_SHARE = "app_share"
const val MUTUAL_MATCH = "mutual_match"
const val ONBOARDING_COMPLETE = "onboarding_complete"
const val ICE_BREAKERS_COMPLETE = "ice_breakers_complete"
const val LIKE_USER = "like_user"
const val DISLIKE_USER = "dislike_user"

// App Review
const val APP_REVIEW_LOVE_IT = "app_review_love_it"
const val APP_REVIEW_NEEDS_WORK = "app_review_needs_work"
const val APP_REVIEW_CLOSED = "app_review_closed"
const val APP_REVIEW_WRITE = "app_review_write"

// User Properties
const val USER_AGE_PROPERTY = "user_age"
const val USER_GENDER_PROPERTY = "user_gender"
const val USER_ORIENTATION_PROPERTY = "user_orientation"
const val LOVE_BEATS_PLUS_USER = "plus_user"

// User property values
const val STRAIGHT = "straight"
const val GAY = "gay"

// Params possible values
const val ANALYTICS_SUCCESS = "success"
const val ANALYTICS_FAILURE = "failure"

const val ANALYTICS_EVENT_SIGN_IN = "ANALYTICS_EVENT_SIGN_IN"
const val ANALYTICS_EVENT_SIGN_IN_FAILURE = "ANALYTICS_EVENT_SIGN_IN_FAILURE"

// Params
const val STATUS = "status"
const val ERROR_REASON = "error_reason"
const val ERROR_CODE = "error_code"


class FirebaseAnalyticsTrackingService {

    companion object {
        fun setUserId(context: Context?, userId: String?) {

            context?.let { ctx ->
                FirebaseAnalytics.getInstance(ctx)
                        .setUserId(userId)
            }
        }

        fun logEvent(context: Context?, name: String,
                     bundle: Bundle? = null) {

            context?.let { ctx ->
                FirebaseAnalytics.getInstance(ctx)
                        .logEvent(name, bundle)
            }
        }

        fun setUserProperty(context: Context?, name: String,
                            value: String?) {

            context?.let { ctx ->
                FirebaseAnalytics.getInstance(ctx)
                        .setUserProperty(name, value)
            }
        }

        fun resetUserProperties(context: Context?) {
            setUserId(context, null)
            setUserProperty(context, USER_AGE_PROPERTY, null)
            setUserProperty(context, USER_GENDER_PROPERTY, null)
            setUserProperty(context, USER_ORIENTATION_PROPERTY, null)
        }
    }
}
