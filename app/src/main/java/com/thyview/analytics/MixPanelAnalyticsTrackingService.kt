package com.thyview.analytics

import android.content.Context
import com.mixpanel.android.mpmetrics.MixpanelAPI
import com.thyview.utils.AppKeys.getMixPanelToken
import org.json.JSONObject
import kotlin.collections.iterator

// EVENTS
const val MP_SCREEN_VIEWED = "screen_viewed"
const val MP_API_STATUS = "api_status"

// Props
const val MP_SCREEN_NAME = "screen_name"
const val MP_API = "api"

// Props values
const val CODE_VERIFICATION = "code_verification"
const val OTP_DIDNT_RECEIVE_CODE = "otp_didnt_receive_code"
const val SIGN_IN_PHONE = "phone"
const val SIGN_IN_GOOGLE = "google"
const val SIGN_IN_TWITTER = "twitter"
const val SIGN_IN_FACEBOOK = "facebook"
const val ACCOUNT_DELETE = "account_delete"
const val MP_GET_USERS = "get_users"
const val MP_GET_SONGS = "get_tracks"
const val MP_CLOUD_VISION = "scan_image_moderation"
const val MP_NUM_LIKE_RESTRICTION = "get_utc_timestamp"
const val MP_USER_PREFS_UPDATED = "user_preferences_updated"
const val MP_SUBSCRIPTION_REGISTRATION = "subscription_registration"

class MixPanelAnalyticsTrackingService {
    companion object {
        private fun getMixPanelInstance(context: Context?): MixpanelAPI? {
            return MixpanelAPI.getInstance(context, getMixPanelToken(), true)
        }
        fun setUserId(context: Context?, externalId: String?) {
            getMixPanelInstance(context)?.identify(externalId)
        }

        fun logEvent(context: Context?, name: String, map: HashMap<String, String> = hashMapOf()) {
            val props = JSONObject()
            for ((key, value) in map) {
                props.put(key, value)
            }
            getMixPanelInstance(context)?.track(name, props)
        }

        fun logApiSuccessEvent(context: Context?, apiName: String) {
            val map = hashMapOf<String, String>()
            map[MP_API] = apiName
            map[STATUS] = ANALYTICS_SUCCESS
            logEvent(context, MP_API_STATUS, map)
        }

        fun logApiFailedEvent(context: Context?, apiName: String, errorReason: String) {
            val map = hashMapOf<String, String>()
            map[MP_API] = apiName
            map[STATUS] = ANALYTICS_FAILURE
            map[ERROR_REASON] = errorReason
            logEvent(context, MP_API_STATUS, map)
        }

        fun setUserProperty(context: Context?, name: String, value: String?) {
            getMixPanelInstance(context)?.people?.set(name, value)
        }

        fun setUserProperty(context: Context?, name: String, value: Boolean) {
            getMixPanelInstance(context)?.people?.set(name, value)
        }
    }
}
