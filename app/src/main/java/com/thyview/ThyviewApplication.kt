package com.thyview

import android.app.Application
import androidx.lifecycle.LifecycleObserver
import com.facebook.FacebookSdk
import com.facebook.appevents.AppEventsLogger
import com.google.firebase.FirebaseApp
import dagger.hilt.android.HiltAndroidApp
import timber.log.Timber

@HiltAndroidApp
class ThyViewApplication: Application(), LifecycleObserver {
    override fun onCreate() {
        super.onCreate()

        // Initialize Facebook SDK before using any Facebook components
        FacebookSdk.sdkInitialize(applicationContext)
        AppEventsLogger.activateApp(this)

        FirebaseApp.initializeApp(this)

        // Setup Timber for logging first to capture all logs
        if (BuildConfig.DEBUG) {
            Timber.plant(Timber.DebugTree())
            Timber.d("Application Started")
        }
    }
}
