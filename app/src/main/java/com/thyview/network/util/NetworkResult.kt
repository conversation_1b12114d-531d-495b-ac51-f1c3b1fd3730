package com.thyview.network.util

sealed class NetworkResult<out R> {

    object Loading: NetworkResult<Nothing>()
    data class Success<out T>(val data: T) : NetworkResult<T>()
    data class Error(val errorMessage: String = "", val exception: Throwable? = null): NetworkResult<Nothing>()

    override fun toString(): String {
        return when (this) {
            is Loading -> "Loading"
            is Success<*> -> "Success[data=$data]"
            is Error -> "Error[exception=$exception]"
        }
    }
}

enum class NetworkResultError(val errorCode: String) {
    NONE("000"),
    RESPONSE_BODY_NULL("0001")
}
