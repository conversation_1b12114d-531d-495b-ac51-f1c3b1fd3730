package com.thyview.network.service.base

import java.io.IOException
import java.net.InetAddress
import java.net.Socket
import java.net.UnknownHostException
import java.security.KeyManagementException
import java.security.NoSuchAlgorithmException
import javax.net.ssl.SSLContext
import javax.net.ssl.SSLSocket
import javax.net.ssl.SSLSocketFactory

/*
These utilities are to support activating TLS v1.2 on Jellybean and KitKat devices
Jellybean and kitkat devices ship with TLS v1.2 protocol support installed but disabled by default
this code supports switching the TLS protocol to TLS v1.2-only and should only be used by Jellybean and kitkat
 */
class TLSV12SocketFactory @Throws(KeyManagementException::class, NoSuchAlgorithmException::class) constructor(): SSLSocketFactory() {

    private val internalSSLSocketFactory: SSLSocketFactory =
        SSLContext.getInstance("TLS").run {
            init(null, null, null)
            socketFactory
        }

    @Throws(IOException::class)
    override fun createSocket(): Socket =
        enableTLSOnSocket(internalSSLSocketFactory.createSocket())


    @Throws(IOException::class)
    override fun createSocket(s: Socket?, host: String?, port: Int, autoClose: Boolean): Socket =
        enableTLSOnSocket(internalSSLSocketFactory.createSocket(s, host, port, autoClose))



    @Throws(IOException::class, UnknownHostException::class)
    override fun createSocket(host: String?, port: Int): Socket =
        enableTLSOnSocket(internalSSLSocketFactory.createSocket(host, port))

    @Throws(IOException::class, UnknownHostException::class)
    override fun createSocket(
        host: String?,
        port: Int,
        localHost: InetAddress?,
        localPort: Int
    ): Socket =
        enableTLSOnSocket(internalSSLSocketFactory.createSocket(host, port, localHost, localPort))

    @Throws(IOException::class)
    override fun createSocket(host: InetAddress?, port: Int): Socket =
        enableTLSOnSocket(internalSSLSocketFactory.createSocket(host, port))

    @Throws(IOException::class)
    override fun createSocket(
        address: InetAddress?,
        port: Int,
        localAddress: InetAddress?,
        localPort: Int
    ): Socket =
        enableTLSOnSocket(internalSSLSocketFactory.createSocket(address, port, localAddress, localPort))

    override fun getDefaultCipherSuites(): Array<String> =
         internalSSLSocketFactory.defaultCipherSuites

    override fun getSupportedCipherSuites(): Array<String> =
         internalSSLSocketFactory.supportedCipherSuites


    private fun enableTLSOnSocket(socket: Socket): Socket =
        socket.apply {
            if (this is SSLSocket)
                enabledProtocols = arrayOf("TLSv1.2")
        }
}