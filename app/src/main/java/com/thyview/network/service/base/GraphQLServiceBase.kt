package com.thyview.network.service.base

import com.apollographql.apollo.ApolloClient
import com.apollographql.apollo.api.Response


abstract class GraphQLServiceBase(): NetworkServiceBase() {

    fun getApolloClient(additionalHeaders: Map<String, String> = emptyMap()): ApolloClient =
        TLSNetworkService.createGraphQlClient(configuration, additionalHeaders)
}

class ApolloServiceError(val response: Response<*>): Error() {
    override val message: String = apolloErrors.toString()
    val apolloErrors: List<com.apollographql.apollo.api.Error>
        get() = response.errors ?: emptyList()
}

fun <T> Response<T>.getValidResponseOrThrowException(): Response<T> =
   if (hasErrors()) {
       throw ApolloServiceError(this)
   } else {
       this
   }

fun <OLD, NEW> Response<OLD>.transform(data: NEW): Response<NEW> =
    Response.builder<NEW>(operation)
        .dependentKeys(dependentKeys)
        .errors(errors)
        .data(data)
        .build()
