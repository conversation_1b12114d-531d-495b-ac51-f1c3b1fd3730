package com.thyview.network.service.base

import com.thyview.network.util.NetworkResultError
import retrofit2.Response
import java.lang.NullPointerException

abstract class RetrofitServiceBase(additionalHeaders: Map<String, String> = emptyMap()): NetworkServiceBase() {
    protected inline fun <reified T> getServiceApi(): T =
        TLSNetworkService.createRestClient(configuration)
}

fun <T> Response<T>.getSuccessBodyOrThrowException(): T {
    if (!isSuccessful) {
        throw RetrofitServiceError(this.code(), this.message(), this.errorBody(), this.headers()["server_tid"])
    }

    this.body()?.let { body ->
        return body
    }?: throw NullPointerException(NetworkResultError.RESPONSE_BODY_NULL.name)
}
