package com.thyview.network.service.base

import okhttp3.Interceptor
import okhttp3.Response
import java.io.IOException
import java.util.UUID

class HostRequestInterceptor(
    private val headersCallback: TLSNetworkService.HeadersCallback,
    private val userAgent: String,
    private val appName: String,
    private val appVersion: String,
    private val customHeadersCallback: TLSNetworkService.CustomHeadersCallback? = null): Interceptor {

    @Throws(IOException::class)
    override fun intercept(chain: Interceptor.Chain): Response {
        val requestBuilder = chain.request().newBuilder()

        requestBuilder.apply {
            addHeader("Accept", "application/json")
            addHeader("Content-Type", "application/json")
            addHeader("User-Agent", userAgent)
            addHeader("X-app", appName)
            addHeader("X-appVersion", appVersion)
            addHeader("txnId", UUID.randomUUID().toString())

            val headers = headersCallback.getAuthHeaders()
            headers.forEach {(key, value) ->
                addHeader(key, value)
            }

            customHeadersCallback?.getCustomHeaders()?.forEach {(key, value) ->
                addHeader(key, value)
            }
        }

        return chain.proceed(requestBuilder.build())
    }
}