package com.thyview.network.service.base

import com.thyview.BuildConfig
import okhttp3.HttpUrl
import okhttp3.Interceptor
import okhttp3.Response
import timber.log.Timber

abstract class NetworkServiceBase(private val additionalHeaders: Map<String, String> = emptyMap()) {

    abstract val baseUrl: HttpUrl

    protected val configuration: TLSNetworkService.Configuration
        get() = TLSNetworkService.Configuration(
            baseUrl = baseUrl,
            interceptors = listOf(
                HostRequestInterceptor(
                    object : TLSNetworkService.HeadersCallback {
                        override fun getAuthHeaders(): Map<String, String> {
                            return mutableMapOf() // Should return auth headers from here
                        }
                    },
                    "user-agent",
                    "app-name",
                    "app-version",
                    object : TLSNetworkService.CustomHeadersCallback {
                        override fun getCustomHeaders(): Map<String, String> {
                            return additionalHeaders
                        }
                    }
                ),
                object : Interceptor {
                    override fun intercept(chain: Interceptor.Chain): Response {
                        val request = chain.request()
                        if (BuildConfig.DEBUG) {
                            Timber.d("Request headers: 'n${request.headers}")
                        }
                        return chain.proceed(request)
                    }
                }
            ),
            debugEnabled = BuildConfig.DEBUG
        )
}