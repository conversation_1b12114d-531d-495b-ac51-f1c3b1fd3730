package com.thyview.network.service.base

import com.apollographql.apollo.ApolloClient
import com.facebook.stetho.okhttp3.StethoInterceptor
import com.google.gson.GsonBuilder
import okhttp3.HttpUrl
import okhttp3.Interceptor
import okhttp3.OkHttpClient
import okhttp3.logging.HttpLoggingInterceptor
import retrofit2.CallAdapter
import retrofit2.Converter
import retrofit2.Retrofit
import retrofit2.adapter.rxjava3.RxJava3CallAdapterFactory
import retrofit2.converter.gson.GsonConverterFactory
import retrofit2.converter.scalars.ScalarsConverterFactory
import java.lang.reflect.Type
import java.util.concurrent.TimeUnit

object TLSNetworkService {

    data class Configuration(
        val baseUrl: HttpUrl,
        val interceptors: List<Interceptor>,
        val callAdapterFactories: List<CallAdapter.Factory> = coreCallAdapterFactories,
        val converterFactories: List<Converter.Factory> = coreConverterFactories,
        val debugEnabled:Boolean = false
    )

    private const val okHttpConnectTimeout: Long = 60000
    private const val okHttpReadTimeout: Long = 60000
    private const val okHttpWriteTimeout: Long = 60000

    private val okHttpClient = OkHttpClient()

    val coreCallAdapterFactories = mutableListOf(
        RxJava3CallAdapterFactory.create()
    )

    val coreConverterFactories = mutableListOf(
        ScalarsConverterFactory.create(),
        GsonConverterFactory.create()
    )

    private val coreDebugNetworkInterceptors = mutableListOf(
        StethoInterceptor()
    )

    val coreDebugInterceptors = mutableListOf(
        HttpLoggingInterceptor().apply { level = (HttpLoggingInterceptor.Level.BODY) }
    )

    @JvmStatic
    inline fun <reified S> createRestClient(configuration: Configuration): S {
        return createRetrofit(configuration).create(S::class.java)
    }

    @PublishedApi
    internal fun createRetrofit(configuration: Configuration): Retrofit {
        val okHttpClient = okHttpClient.newBuilder()
            .connectTimeout(okHttpConnectTimeout, TimeUnit.MILLISECONDS)
            .readTimeout(okHttpReadTimeout, TimeUnit.MILLISECONDS)
            .writeTimeout(okHttpWriteTimeout, TimeUnit.MILLISECONDS)
            .also { builder ->
                if (configuration.debugEnabled) {
                    coreDebugInterceptors.forEach { builder.addInterceptor(it) }
                    coreDebugNetworkInterceptors.forEach { builder.addNetworkInterceptor(it) }
                }
                configuration.interceptors.forEach { builder.addInterceptor(it) }
            }
            .build()

        return Retrofit.Builder()
            .baseUrl(configuration.baseUrl)
            .client(okHttpClient)
            .also { builder ->
                configuration.callAdapterFactories.forEach { builder.addCallAdapterFactory(it) }
            }
            .also { builder ->
                configuration.converterFactories.forEach { builder.addConverterFactory(it) }
            }
            .build()
    }

    @JvmStatic
    fun createGraphQlClient(
        configuration: Configuration,
        additionalHeaders: Map<String, String> = emptyMap()): ApolloClient {
        val okHttpClient = okHttpClient.newBuilder()
            .connectTimeout(okHttpConnectTimeout, TimeUnit.MILLISECONDS)
            .readTimeout(okHttpReadTimeout, TimeUnit.MILLISECONDS)
            .writeTimeout(okHttpWriteTimeout, TimeUnit.MILLISECONDS)
            .also { builder ->
                if (configuration.debugEnabled) {
                    coreDebugInterceptors.forEach { builder.addInterceptor(it) }
                    coreDebugNetworkInterceptors.forEach { builder.addNetworkInterceptor(it) }
                }
                configuration.interceptors.forEach { builder.addInterceptor(it) }
                additionalHeaders.takeIf { it.isNotEmpty() }.let {
                    builder.addInterceptor { chain ->
                        val request = chain.request()
                        val interceptorBuilder =
                            request.newBuilder().method(request.method, request.body)
                        additionalHeaders.forEach {entry ->
                            interceptorBuilder.addHeader(entry.key, entry.value)
                        }
                        chain.proceed(interceptorBuilder.build())
                    }
                }
            }
            .build()

        return ApolloClient.builder()
            .serverUrl(configuration.baseUrl)
            .okHttpClient(okHttpClient)
            .build()
    }

    @JvmStatic
    fun createCustomerGsonConverter(type: Type, typeAdapter: Any): GsonConverterFactory =
        GsonBuilder()
            .registerTypeAdapter(type, typeAdapter)
            .create()
            .run { GsonConverterFactory.create(this) }

    interface HeadersCallback {
        fun getAuthHeaders(): Map<String, String>
    }

    interface CustomHeadersCallback {
        fun getCustomHeaders(): Map<String, String>
    }
}