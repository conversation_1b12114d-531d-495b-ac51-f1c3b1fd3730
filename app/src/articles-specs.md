# Articles API Specification

This document provides detailed specifications for all API endpoints in the Articles AWS backend.

## Articles


### Get Articles List
- **Endpoint:** `GET /articles`
- **Handler:** `functions/getArticles.handler`
- **Description:** Retrieves a paginated list of articles
- **Query Parameters:**
    - `limit` (optional): Number of articles to retrieve (default: 20)
    - `lastKey` (optional): Key for pagination
    - `postedBy` (optional): Filter by author ID
    - `tags` (optional): Comma-separated tags to filter
    - `sortBy` (optional): Field to sort by ('createdAt' or 'likes')
    - `order` (optional): Sort order ('asc' or 'desc')
    - `userId` (optional): Include like status for this user
- **Response:**
  ```json
  {
    "success": true,
    "data": {
      "items": [
        {
          "id": "article-id",
          "title": "Article Title",
          "description": "Article description text",
          "imageUrl": "https://example.com/image.jpg",
          "contentUrl": "https://example.com/content.html",
          "likes": 42,
          "createdAt": "2025-05-15T12:00:00Z",
          "modifiedAt": "2025-05-15T12:00:00Z",
          "postedBy": "author-id",
          "additionalNotes": "Additional notes about the article",
          "content": "Article content text or HTML",
          "tags": ["technology", "programming"],
          "isLikedByUser": true // Only when userId is provided
        }
      ],
      "nextKey": "key-for-next-page"
    }
  }
  ```

### Like Article
- **Endpoint:** `POST /articles/like`
- **Handler:** `functions/likeArticle.handler`
- **Description:** Likes an article
- **Request:**
  ```json
  {
    "articleId": "article-id",
    "userId": "user-id"
  }
  ```
- **Response:**
  ```json
  {
    "success": true,
    "message": "Article liked successfully"
  }
  ```

### Unlike Article
- **Endpoint:** `POST /articles/unlike`
- **Handler:** `functions/unlikeArticle.handler`
- **Description:** Unlikes an article
- **Request:**
  ```json
  {
    "articleId": "article-id",
    "userId": "user-id"
  }
  ```
- **Response:**
  ```json
  {
    "success": true,
    "message": "Article unliked successfully"
  }
  ````

All endpoints may return these error responses:

### Bad Request (400)
```json
{
  "success": false,
  "error": "Validation error message"
}
```

### Not Found (404)
```json
{
  "success": false,
  "error": "Article not found"
}
```

### Server Error (500)
```json
{
  "success": false,
  "error": "Internal server error"
}
```