# API Specification for Cloud Functions

All functions are exposed as Firebase Callable Functions. Invoke via
```kotlin
```

---

## createPost

- **Input**
  ```json
  {
    "text":         "Optional post text",
    "imageUrl":     "Optional image URL"
  }
  ```
- **Output**
  ```json
  {
    "postId": "string"
  }
  ```
- **Description**  
  Creates a new post under `/posts/{postId}`, denormalizing:
  - `externalAuthorId` (from `auth.uid`)
  - `profileImageUrl` (from `/users/{uid}`)
  - Initializes `likeCount = 0`, `commentCount = 0`.

---

## getPostsList

- **Input**
  ```json
  {
    "limit":                 number,        // 1–50
    "startAfterCreatedAt":   "ISO8601"      // optional cursor
  }
  ```
- **Output**
  ```json
  {
    "posts": [
      {
        "id":               "string",
        "externalAuthorId": "string",
        "username":         "string",
        "profileImageUrl":  "string|null",
        "text":             "string",
        "imageUrl":         "string|null",
        "likeCount":        number,
        "commentCount":     number,
        "createdAt":        "ISO8601",
        "liked":            boolean,
        "viewCount":        number
      }
      // … up to `limit` items
    ]
  }
  ```
- **Description**  
  Returns a page of posts sorted newest→oldest, including whether the current user has liked each.

---

## getPostDetail

- **Input**
  ```json
  { "postId": "string" }
  ```
- **Output**
  ```json
  {
    "post": {
      /* same shape as getPostsList.posts[0] */
    },
    "comments": [
      {
        "id":               "string",
        "externalAuthorId": "string",
        "username":         "string",
        "text":             "string",
        "parentCommentId":  null,
        "imageUrl":         "string|null",
        "createdAt":        "ISO8601",
        "replyCount":       number,
        "liked":            boolean
      }
      // … top-level comments
    ]
  }
  ```
- **Description**  
  Fetches a single post and its top-level comments. Comments include `replyCount` and whether the current user has liked them.

---

## likePost

- **Input**
  ```json
  { "postId": "string" }
  ```
- **Output**
  ```json
  { "liked": boolean }
  ```
- **Description**  
  Toggles like/unlike for the authenticated user under  
  `/posts/{postId}/likes/{uid}` and increments/decrements `likeCount`.

---

## hasUserLiked

- **Input**
  ```json
  { "postId": "string" }
  ```
- **Output**
  ```json
  { "liked": boolean }
  ```
- **Description**  
  Checks if `/posts/{postId}/likes/{uid}` exists.

---

## editPost

- **Input**
  ```json
  {
    "postId":   "string",
    "text":     "Optional new text",
    "imageUrl": "Optional new image URL"
  }
  ```
- **Output**
  ```json
  { "success": true }
  ```
- **Description**  
  Allows the original author to update their post.

---

## deletePost

- **Input**
  ```json
  { "postId": "string" }
  ```
- **Output**
  ```json
  { "success": true }
  ```
- **Description**  
  Deletes the post and all its subcollections (`comments`, `likes`, etc.).

---

## reportPost

- **Input**
  ```json
  {
    "postId": "string",
    "reason": "string"
  }
  ```
- **Output**
  ```json
  { "success": true }
  ```
- **Description**  
  Records a report under `/reports/posts/{postId}/{uid}`.

---

## reportComment

- **Input**
  ```json
  {
    "postId":    "string",
    "commentId": "string",
    "reason":    "string"
  }
  ```
- **Output**
  ```json
  { "success": true }
  ```
- **Description**  
  Records a report under `/reports/comments/{commentId}_on_{postId}/{uid}`.

---

## getComments

- **Input**
  ```json
  { "postId": "string" }
  ```
- **Output**
  ```json
  {
    "comments": [
      {
        "id":               "string",
        "externalAuthorId": "string",
        "username":         "string",
        "text":             "string",
        "parentCommentId":  null,
        "imageUrl":         "string|null",
        "createdAt":        "ISO8601",
        "replyCount":       number,
        "liked":            boolean
      }
      // … top-level only
    ]
  }
  ```
- **Description**  
  Reads all top-level comments, plus whether the user has liked each, and the number of direct replies.

---

## submitComment

- **Input**
  ```json
  {
    "postId":          "string",
    "text":            "string",
    "parentCommentId": "string|null",
    "imageUrl":        "string|null"
  }
  ```
- **Output**
  ```json
  { "commentId": "string" }
  ```
- **Description**  
  Creates a comment or reply, increments:
  - `posts/{postId}.commentCount` if top-level
  - `posts/{postId}/comments/{parent}.replyCount` if a reply

---

## deleteComment

- **Input**
  ```json
  {
    "postId":    "string",
    "commentId": "string"
  }
  ```
- **Output**
  ```json
  { "success": true }
  ```
- **Description**  
  Deletes a comment and its direct replies; adjusts `commentCount` or parent `replyCount` accordingly.

---

## getReplies

- **Input**
  ```json
  {
    "postId":    "string",
    "commentId": "string"
  }
  ```
- **Output**
  ```json
  {
    "replies": [
      {
        "id":               "string",
        "externalAuthorId": "string",
        "username":         "string",
        "text":             "string",
        "parentCommentId":  "string",
        "imageUrl":         "string|null",
        "createdAt":        "ISO8601",
        "liked":            boolean
      }
      // … one-level replies only
    ]
  }
  ```
- **Description**  
  Fetches direct replies to a given comment, including like state.

---

## likeComment

- **Input**
  ```json
  {
    "postId":    "string",
    "commentId": "string"
  }
  ```
- **Output**
  ```json
  { "liked": boolean }
  ```
- **Description**  
  Toggles like/unlike on `/posts/{postId}/comments/{commentId}/likes/{uid}` and updates that comment’s `likeCount`.

---

## getImageUploadUrl

- **Input**
  ```json
  {
    "fileName":    "string",
    "contentType": "string"
  }
  ```
- **Output**
  ```json
  {
    "uploadUrl": "string",
    "publicUrl": "string"
  }
  ```
- **Description**  
  Generates signed upload+download URLs for Firebase Storage.
