// Top-level build file where you can add configuration options common to all sub-projects/modules
buildscript {
    ext {
        kotlin_version = '2.0.0'  // Updated Kotlin version
        hilt_version = '2.51.1'      // Updated Hilt version
        agp_version = '8.9.0'      // Updated to latest stable AGP version
        compose_compiler_version = '2.0.0' // Should match kotlin_version
        nav_version = '2.8.9' // Added navigation version
    }
    
    repositories {
        google()
        mavenCentral()
    }
    
    dependencies {
        // Add the Google services Gradle plugin
        classpath 'com.google.gms:google-services:4.4.2'
        classpath 'com.google.firebase:firebase-crashlytics-gradle:3.0.3'
        classpath 'com.google.firebase:perf-plugin:1.4.2'
        // Add the Safe Args plugin
        classpath "androidx.navigation:navigation-safe-args-gradle-plugin:$nav_version"
    }
}

plugins {
    id 'com.android.application' version "$agp_version" apply false
    id 'com.android.library' version "$agp_version" apply false
    id "org.jetbrains.kotlin.android" version "$kotlin_version" apply false
    id 'com.google.dagger.hilt.android' version "$hilt_version" apply false
    id 'org.jetbrains.kotlin.plugin.compose' version "$kotlin_version" apply false // Match kotlin_version
}

tasks.register('clean', Delete) {
    delete rootProject.buildDir
}
